分析思路：分析从数据逻辑、业务逻辑、代码逻辑、表之间的关联逻辑去分析。

## 涉及的表
- `a_test_log`
- `av_qm_si_gist`
- `av_qm_si_status`
- `av_ss_status_100`
- `av_ss_status_200`
- `cr_rtn_h`
- `ea`
- `me_finish_io`
- `me_finish_io_h`
- `mo`
- `mo_rmb`
- `mo_rmb_ea`
- `pd_par
- `qm_si_lot_b_sn`
- `qm_si_lot_h`
- `qm_si_status_sy_type`
- `ss_cdvl`
- `ss_factory`
- `ss_io_tbl`
- `ss_user`
- `szjy_mes_cr_rtn_b`
- `szjy_mes_cr_rtn_h`
- `szjy_mes_cr_rtn_sn_part`
- `szjy_mo_apply_me_finish`
- `szjy_oqc_gc_disqualified`
- `szjy_qm_oba_sn`
- `wm_sn`
- `wm_temp_receipt`
- `wm_temp_receipt_b`
- `wm_temp_receipt_sn_part`
