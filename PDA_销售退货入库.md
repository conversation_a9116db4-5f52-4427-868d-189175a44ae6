销售退货入库:af_pda_wms_sales_rtn_inbound

```sql
-- DROP FUNCTION public.af_pda_wms_sales_rtn_inbound(varchar);



**CREATE** **OR** **REPLACE** **FUNCTION** public.af_pda_wms_sales_rtn_inbound(datas **character** **varying**)

 **RETURNS** **character** **varying**

 **LANGUAGE** plpgsql

**AS** **$function$**

/*

\*  功能：  销售退货扫描

\*  描述：  

\*  时间：  

\*  开发者：

*/

​	**declare**

​		json_datas **json**;

​		_user_id **text**;

​		_user_no **text**;

​		_user_name **text**;

​		_host **text**;



​		_bill_no **text**;

​		_bill_id **text**;

​		_sn_no **text**;

​		_need_new_sn **bool**;



​		json_result **json**;



​		_err_msg_text **text**;

​		_err_pg_detail **text**;

​		_err_msg **text**;			

​		res returntype;

​	**BEGIN**

​		json_datas := **json**(datas);

​		_user_no := json_datas->>'user_no';



​		json_datas := **json**(json_datas->'datas'->0);

​		_bill_no := json_datas->>'bill_no';

​		_sn_no := json_datas->>'sn_no';





​		**select** cr_rtn_h_id,need_new_sn **into** _bill_id,_need_new_sn **from** szjy_mes_cr_rtn_h **where** cr_rtn_h_no=_bill_no;



​		**if** **not** **exists**(**select** 1 **from** szjy_mes_cr_rtn_h **where** cr_rtn_h_no=_bill_no) **then**

​			res := **row**('false', '扫描销售退货单不存在。');

​			**return** **to_json**(res);

​		**end** **if**;



​		**if** **exists**(**select** 1 **from** szjy_mes_cr_rtn_h **where** cr_rtn_h_no=_bill_no **and** **coalesce**(cr_rtn_rmk06,'')='仓库扫描收货完成') **then**

​			res := **row**('false', '扫描销售退货单已经收货完成，不能收货。');

​			**return** **to_json**(res);

​		**end** **if**;



​		**if** **not** **exists**(**select** 1 **from** wm_sn **where** sn_no=_sn_no **and** sn_status='830') **then**

​			res := **row**('false', '扫描产品不是出库状态，不能退货。');

​			**return** **to_json**(res);

​		**end** **if**;



​		**if** _need_new_sn=**true** **then**

​			json_result := af_pda_wms_sales_rtn_new_sn(datas);

​		**else**

​			json_result := af_pda_wms_sales_rtn_orig_sn(datas);

​		**end** **if**;



​		res := **row**(json_result->>'successful', json_result->>'msg');

​		**return** **to_json**(res);



​	**exception** **when** **others** **then**

​		**GET** STACKED **diagnostics**

​			_err_msg_text = MESSAGE_TEXT,

​			_err_pg_detail = PG_EXCEPTION_DETAIL;

​	

​		_err_msg := **format**('错误信息:%s,详情:%s',_err_msg_text,_err_msg_text);

​		res := **row**('false',_err_msg);

​		**return** **to_json**(res);



​	**END**;

**$function$**

;


```





af_pda_wms_sales_rtn_new_sn：

```sql
-- DROP FUNCTION public.af_pda_wms_sales_rtn_new_sn(varchar);



**CREATE** **OR** **REPLACE** **FUNCTION** public.af_pda_wms_sales_rtn_new_sn(datas **character** **varying**)

 **RETURNS** **character** **varying**

 **LANGUAGE** plpgsql

**AS** **$function$**

/*

\*  功能：  销售退货扫描

\*  描述：  

\*  时间：  

\*  开发者：

*/

​	**declare**

​		json_datas **json**;

​		_user_id **text**;

​		_user_no **text**;

​		_user_name **text**;

​		_host **text**;



​		_bill_no **text**;

​		_bill_id **text**;

​		_sn_no **text**;

​		_rtn_b_id **text**;



​		_err_msg_text **text**;

​		_err_pg_detail **text**;

​		_err_msg **text**;			

​		res returntype;



​	**BEGIN**



​		json_datas := **json**(datas);

​		_user_no := json_datas->>'user_no';



​		json_datas := **json**(json_datas->'datas'->0);

​		_bill_no := json_datas->>'bill_no';

​		_sn_no := json_datas->>'sn_no';



​		**insert** **into** a_test_log **values**(datas, 'new_sn', **localtimestamp**);



​		**select** user_id,user_name **into** _user_id,_user_name **from** ss_user **where** user_no=_user_no;

​		**select** cr_rtn_h_id **into** _bill_id **from** szjy_mes_cr_rtn_h **where** cr_rtn_h_no=_bill_no;



​		**if** **not** **exists**(**select** 1 **from** szjy_mes_cr_rtn_sn_part **where** cr_rtn_h_id=_bill_id **and** sn_no=_sn_no) **then**

​			_err_msg := **format**('扫描退货产品标签【%s】不属于此销售退货单【%s】', _sn_no, _bill_no);

​			res := **row**('false', _err_msg);

​			**return** **to_json**(res);

​		**end** **if**;



​		**if** **exists**(**select** 1 **from** szjy_mes_cr_rtn_sn_part **where** cr_rtn_h_id=_bill_id **and** sn_no=_sn_no **and** **coalesce**(cr_rtn_rmk06,'')='仓库扫描收货完成') **then**

​			_err_msg := **format**('扫描退货产品标签【%s】已经收货完成，无需要二次扫描收货。', _sn_no);

​			res := **row**('false', '');

​			**return** **to_json**(res);

​		**end** **if**;



​		**select** cr_rtn_rmk01 **into** _rtn_b_id **from** szjy_mes_cr_rtn_sn_part **where** cr_rtn_h_id=_bill_id **and** sn_no=_sn_no;



​		**update** public.szjy_mes_cr_rtn_b **set** part_qty_real=part_qty_plan,cr_rtn_rmk06='仓库扫描收货完成',

​			upd_time=**localtimestamp**,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name

​		**where** cr_rtn_b_id=_rtn_b_id;



​		**update** public.szjy_mes_cr_rtn_sn_part **set** cr_rtn_rmk06='仓库扫描收货完成',

​			upd_time=**localtimestamp**,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name

​		**where** cr_rtn_h_id=_bill_id **and** sn_no=_sn_no;



​		**update** public.wm_sn **set** sn_status='800',sn_status_name='在库',

​			upd_time=**localtimestamp**,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name

​		**where** sn_no=_sn_no;



​		**if** **not** **exists**(**select** **distinct** 1 **from** szjy_mes_cr_rtn_b **where** cr_rtn_h_id=_bill_id **and** **coalesce**(cr_rtn_rmk06,'') != '仓库扫描收货完成') **then**

​			**update** public.szjy_mes_cr_rtn_h **set** cr_rtn_rmk06='仓库扫描收货完成',

​				upd_time=**localtimestamp**,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name

​			**where** cr_rtn_h_id=_bill_id;

​		**end** **if**;



​		res := **row**('true', '----销售退货完成----');

​		**return** **to_json**(res);



​	**exception** **when** **others** **then**

​		**GET** STACKED **diagnostics**

​			_err_msg_text = MESSAGE_TEXT,

​			_err_pg_detail = PG_EXCEPTION_DETAIL;

​	

​		_err_msg := **format**('错误信息:%s,详情:%s',_err_msg_text,_err_msg_text);

​		res := **row**('false',_err_msg);

​		**return** **to_json**(res);

​	**END**;

**$function$**

;
```



af_pda_wms_sales_rtn_orig_sn：

```sql
-- DROP FUNCTION public.af_pda_wms_sales_rtn_orig_sn(varchar);



**CREATE** **OR** **REPLACE** **FUNCTION** public.af_pda_wms_sales_rtn_orig_sn(datas **character** **varying**)

 **RETURNS** **character** **varying**

 **LANGUAGE** plpgsql

**AS** **$function$**

/*

\*  功能：  销售退货扫描

\*  描述：  

\*  时间：  

\*  开发者：

*/

​	**declare**

​		json_datas **json**;

​		_user_id **text**;

​		_user_no **text**;

​		_user_name **text**;

​		_host **text**;



​		_bill_no **text**;

​		_bill_id **text**;

​		_sn_no **text**;

​		_part_no **text**;

​		_part_qty **numeric**;

​		_part_qty_plan **numeric**;

​		_part_qty_real **numeric**;

​		_invp_area **text**;

​		_lot_no **text**;

​		_sn_type **text**;



​		row_datas record;



​		_err_msg_text **text**;

​		_err_pg_detail **text**;

​		_err_msg **text**;			

​		res returntype;



​	**BEGIN**

​		json_datas := **json**(datas);

​		_user_no := json_datas->>'user_no';



​		json_datas := **json**(json_datas->'datas'->0);

​		_bill_no := json_datas->>'bill_no';

​		_sn_no := json_datas->>'sn_no';



​		**select** user_id,user_name **into** _user_id,_user_name **from** ss_user **where** user_no=_user_no;

​		**select** cr_rtn_h_id **into** _bill_id **from** szjy_mes_cr_rtn_h **where** cr_rtn_h_no=_bill_no;



​		**if** **exists**(**select** 1 **from** szjy_mes_cr_rtn_sn_part **where** cr_rtn_h_id=_bill_id **and** sn_no=_sn_no ) **then**

​			res := **row**('false', '扫描产品已经退货扫描，不能二次扫描。');

​			**return** **to_json**(res);

​		**end** **if**;



​		**select** sn_type,part_no,part_qty **into** _sn_type,_part_no,_part_qty **from** wm_sn **where** sn_no=_sn_no;



​		**if** **not** **exists**(**select** **distinct** 1 **from** szjy_mes_cr_rtn_b **where** cr_rtn_h_id=_bill_id **and** part_no=_part_no ) **then**

​			res := **row**('false', '扫描产品编码不属于销售退货单内，不能销售退货。');

​			**return** **to_json**(res);

​		**end** **if**;



​		**select** invp_area,lot_no,**sum**(part_qty_plan) **into** _invp_area,_lot_no,_part_qty_plan 

​		**from** szjy_mes_cr_rtn_b **where** cr_rtn_h_id=_bill_id **and** part_no=_part_no **group** **by** invp_area,lot_no;



​		**select** **coalesce**(**sum**(part_qty),0) **into** _part_qty_real **from** szjy_mes_cr_rtn_sn_part **where** cr_rtn_h_id=_bill_id **and** part_no=_part_no;



​		**if** _part_qty_real+_part_qty > _part_qty_plan **then**

​			_err_msg := **format**('此退货单【%s】已经收货【%s】，现收货【%s】，超出退货单数量【%s】，不能收货。', _bill_no,_part_qty_real,_part_qty,_part_qty_plan);

​			res := **row**('false', _err_msg);

​			**return** **to_json**(res);

​		**end** **if**;		



​		**insert** **into** public.szjy_mes_cr_rtn_sn_part

​		(cr_rtn_sn_id, cr_rtn_h_id, sn_no, part_no, part_name, part_spec, part_unit, part_qty, lot_no, invp_area, cr_rtn_rmk01, cr_rtn_rmk02, cr_rtn_rmk03, cr_rtn_rmk04, cr_rtn_rmk05, cr_rtn_rmk06, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host)

​		**select** af_auid(), _bill_id, _sn_no, _part_no, part_name, part_spec, part_unit, _part_qty, _lot_no, _invp_area, '', '', '', '', '', '仓库扫描收货完成', **localtimestamp**, _user_id, _user_no, _user_name, _host, **localtimestamp**, _user_id, _user_no, _user_name, _host

​		**from** pd_part

​		**where** part_no=_part_no;	



​		**update** public.wm_sn **set** sn_status='800',sn_status_name='在库',inventory_lot=_lot_no,invp_area_no=_invp_area,

​			upd_time=**localtimestamp**,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name

​		**where** sn_no=_sn_no;



​		**if** _sn_type='40' **then**

​			**update** public.wm_sn **set** sn_status='800',sn_status_name='在库',inventory_lot=_lot_no,invp_area_no=_invp_area,

​				upd_time=**localtimestamp**,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name

​			**where** sn_pack_50=_sn_no;	

​		**end** **if**;



​		**if** _part_qty_real+_part_qty = _part_qty_plan **then**



​			**update** public.szjy_mes_cr_rtn_b **set** part_qty_real=part_qty_plan,cr_rtn_rmk06='仓库扫描收货完成',

​				upd_time=**localtimestamp**,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name

​			**where** cr_rtn_h_id=_bill_id **and** part_no=_part_no;



​		**end** **if**;



​		**if** **not** **exists**(**select** **distinct** 1 **from** szjy_mes_cr_rtn_b **where** cr_rtn_h_id=_bill_id **and** **coalesce**(cr_rtn_rmk06,'') != '仓库扫描收货完成') **then**

​			**update** public.szjy_mes_cr_rtn_h **set** cr_rtn_rmk06='仓库扫描收货完成',

​				upd_time=**localtimestamp**,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name

​			**where** cr_rtn_h_id=_bill_id;

​		**end** **if**;



​		res := **row**('true', '----销售退货完成----');

​		**return** **to_json**(res);



​	**exception** **when** **others** **then**

​		**GET** STACKED **diagnostics**

​			_err_msg_text = MESSAGE_TEXT,

​			_err_pg_detail = PG_EXCEPTION_DETAIL;

​	

​		_err_msg := **format**('错误信息:%s,详情:%s',_err_msg_text,_err_msg_text);

​		res := **row**('false',_err_msg);

​		**return** **to_json**(res);

​	**END**;

**$function$**

;
```

