# 生产完成品入库查询：

```sql
SELECT me_finish_io_no,status_name,finish_io_status,finish_io_datetime,ea_type3,mo_no,part_no,part_spec,lot_no,finish_io_qty_ok,workshop_no,me_finish_io_rmk1,workshop_worker_no,workshop_worker_name,me_finish_io_rmk4,io_times,crt_time,upd_time,upd_user_no,si_lot_h_status_name,si_conclusion_name,inspec_user_name,si_sample_qty FROM (select ass.status_name ,e.ea_type3,st.cdvl_name as si_lot_h_status_name,qm.si_conclusion_name,qm.si_sample_qty,case when coalesce(qm.si_conclusion_no,'')<>'' then qm.upd_user_name else '' end as inspec_user_name,h.* 
	from me_finish_io_h h 
	left join av_ss_status_200 ass on ass.status_no = h.finish_io_status 
	left join ea e on e.ea_no = h.workshop_no 
	left join qm_si_lot_h qm on qm.move_order_no = h.me_finish_io_no
	left join av_qm_si_status st on st.cdvl_id = qm.si_lot_h_status
order by h.finish_io_datetime desc) Tb  WHERE (crt_time >= CURRENT_DATE - INTERVAL '6 months'  
  AND crt_time < CURRENT_DATE + INTERVAL '1 day' ) 
  
```





## 完工入库交易明细：

```sql
SELECT me_finish_io_id,me_finish_io_no,sn_no,finish_io_datetime,mo_no,part_no,part_name,part_spec,part_unit,lot_no,finish_io_qty_ok,finish_io_qty_ng,finish_io_qty_scrap,me_finish_io_rmk4,upd_time,upd_user_no,upd_user_name FROM ( SELECT a.me_finish_io_id,
    a.me_finish_io_no,
    a.finish_io_status,
    a.finish_io_datetime,
    a.factory_no,
    a.mo_no,
    a.part_no,
    a.part_name,
    a.part_spec,
    a.part_unit,
    a.part_idt,
    a.lot_no,
    a.finish_io_qty_ok,
    a.invp_no_ok,
    a.finish_io_qty_ng,
    a.invp_no_ng,
    a.finish_io_qty_scrap,
    a.invp_no_scrap,
    a.finish_io_qty_other,
    a.invp_no_other,
    a.me_finish_io_rmk1,
    a.me_finish_io_rmk2,
    a.me_finish_io_rmk3,
    a.me_finish_io_rmk4,
    a.fb_id,
    a.crt_time,
    a.crt_user,
    a.crt_user_no,
    a.crt_user_name,
    a.crt_host,
    a.upd_time,
    a.upd_user,
    a.upd_user_no,
    a.upd_user_name,
    a.upd_host,
    a.io_is_sucessed,
    a.io_times,
    a.io_last_time,
    b.status_name,
    c.factory_name,
    a.sn_no
   FROM me_finish_io a
     LEFT JOIN av_ss_status_100 b ON a.finish_io_status::text = b.status_no::text
     LEFT JOIN ss_factory c ON a.factory_no::text = c.factory_no::text) Tb  WHERE me_finish_io_no = 'SCRK202507010024' 
```

