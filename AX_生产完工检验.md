```sql
SELECT si_lot_h_id,crt_time,si_lot_h_no,si_lot_h_status_name,si_conclusion_name,part_no,part_name,part_spec,order_no,si_lot_h_rmk03,si_lot_move_type_name,si_type_name,si_lot_qty,si_degree_name,order_type,qm_si_gist_name,client_no,client_name,move_order_no,crt_user_name,upd_time,upd_user_name,si_level,si_level_name,si_aql,si_lot_qty_ok,si_lot_qty_ng,si_sample_qty,ea_no,ea_name FROM (SELECT a.si_lot_h_id,
    a.si_lot_h_no,
    a.si_lot_h_status,
    a.factory_no,
    a.factory_name,
    a.part_no,
    a.part_name,
    a.part_spec,
    a.part_idt,
    a.wkp_no,
    a.wkp_name,
    a.si_lot_qty,
    a.si_lot_move_type,
    a.si_lot_move_type_name,
    a.move_order_h_id,
    a.move_order_b_id,
    a.move_order_id,
    a.move_order_no,
    a.order_type,
    a.order_type_name,
    a.order_h_id,
    a.order_b_id,
    a.order_id,
    a.order_no,
    a.client_no,
    a.client_name,
    a.supplier_no,
    a.supplier_name,
    a.si_type,
    a.si_type_name,
    a.si_degree,
    a.si_degree_name,
    a.si_level,
    a.si_level_name,
    a.si_aql,
    a.si_lot_qty_ok,
    a.si_lot_qty_ng,
    a.si_conclusion_no,
    a.si_conclusion_name,
    a.si_is_pass,
    a.si_lot_h_rmk01,
    a.si_lot_h_rmk02,
    a.si_lot_h_rmk03,
    a.si_lot_h_rmk04,
    a.si_lot_h_rmk05,
    a.si_lot_h_rmk06,
    a.si_lot_h_rmk07,
    a.si_lot_h_rmk08,
    a.si_lot_h_rmk09,
    a.si_lot_h_rmk10,
    a.si_lot_h_rmk11,
    a.si_lot_h_rmk12,
    a.si_lot_h_rmk13,
    a.si_lot_h_rmk14,
    a.si_lot_h_rmk15,
    a.si_lot_h_rmk16,
    a.si_lot_h_rmk17,
    a.si_lot_h_rmk18,
    a.si_lot_h_rmk19,
    a.si_lot_h_rmk20,
    a.si_lot_h_rmk21,
    a.si_lot_h_rmk22,
    a.si_lot_h_rmk23,
    a.si_lot_h_rmk24,
    a.si_lot_h_rmk25,
    a.si_lot_h_rmk26,
    a.si_lot_h_rmk27,
    a.si_lot_h_rmk28,
    a.si_lot_h_rmk29,
    a.si_lot_h_rmk30,
    a.si_lot_h_rmk32,
    a.da_switch_id,
    a.crt_time,
    a.crt_user,
    a.crt_user_no,
    a.crt_user_name,
    a.crt_host,
    a.upd_time,
    a.upd_user,
    a.upd_user_no,
    a.upd_user_name,
    a.upd_host,
    a.io_is_sucessed,
    a.io_times,
    a.io_last_time,
    a.ea_no,
    a.ea_name,
    b.cdvl_name AS si_lot_h_status_name,
    sy.sy_status_type_name,
    st.qm_si_gist_no,
    st.qm_si_gist_name,
    a.si_sample_qty,
    a.ng_count,
    a.ng_level,
    a.ng_ways,
    a.ng_ways_name,
    a.is_8d,
    a.file_path
    ,a.cancel_reason,a.check_status,a.qe_name,a.qe_time,a.sqe_name,a.sqe_time

   FROM qm_si_lot_h a
     LEFT JOIN ss_cdvl b ON a.si_lot_h_status::text = b.cdvl_id::text
     left join qm_si_status_sy_type sy on a.si_lot_h_rmk32 = sy.sy_status_type_no
     left join av_qm_si_gist st on st.qm_si_gist_no = a.qm_si_gist_no
    order by a.crt_time desc

) Tb  WHERE (si_lot_move_type='310') 

```