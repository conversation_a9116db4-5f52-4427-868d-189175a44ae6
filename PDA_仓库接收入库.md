```sql
-- DROP FUNCTION public.af_pda_wms_prod_inbound(varchar);

CREATE OR REPLACE FUNCTION public.af_pda_wms_prod_inbound(datas character varying)
 RETURNS character varying
 LANGUAGE plpgsql
AS $function$
/*
 * 功能：  仓库入库（本厂，分厂及委外厂产品）
 * 描述：  
 * 时间：  2024/07
 * 开发者：
 * 数据：  
 */
	declare 
		json_datas json;
		_user_id text;
		_user_no text;
		_user_name text;
		_host text;
	
		_bill_no text;
		_sn_no text;
		_inbound_org text;
		json_result json;
	
		_err_msg_text text;
		_err_pg_detail text;
		_err_msg text;		
		res returntype;
	begin
		json_datas := json(datas);
		_user_no := json_datas->>'user_no';
	
		json_datas := json(json_datas->'datas'->0);
		_bill_no := json_datas->>'bill_no';		
		_sn_no := json_datas->>'sn_no';	
	
		--insert into a_test_log values(datas,'wms_prod_inbound',localtimestamp);
	
		--select me_finish_io_rmk1 into _inbound_org from me_finish_io_h where me_finish_io_no=_bill_no;

		select (case when workshop_no='J129' then 'JXML' when workshop_no='J171' then 'JXRS' else workshop_no end) into _inbound_org 
		from me_finish_io_h where me_finish_io_no=_bill_no;

		if coalesce(_inbound_org, '')='' then
			res := row('false', '此入库单没有指定入库组织，不能入库');
			return to_json(res);
		end if;
		
		if strpos(_inbound_org,'JX')=1 then
			json_result := af_pda_wms_prod_temp_receipt(json_build_object('user_no',_user_no,'bill_no',_bill_no,'sn_no',_sn_no)::text);
		else
			json_result := af_pda_wms_prod_factory_inbound(json_build_object('user_no',_user_no,'bill_no',_bill_no,'sn_no',_sn_no)::text);
		end if;
	
		res := row(json_result->>'successful', json_result->>'msg');
		return to_json(res);

	EXCEPTION WHEN OTHERS THEN 
		GET STACKED DIAGNOSTICS 
			_err_msg_text = MESSAGE_TEXT,
			_err_pg_detail = PG_EXCEPTION_DETAIL;
	
		_err_msg := format('错误信息:%s,详情:%s',_err_msg_text,_err_msg_text);
		res := row('false',_err_msg);
		return to_json(res);		

	END;
$function$
;

```



```sql
-- DROP FUNCTION public.af_pda_wms_prod_factory_inbound(varchar);

CREATE OR REPLACE FUNCTION public.af_pda_wms_prod_factory_inbound(datas character varying)
 RETURNS character varying
 LANGUAGE plpgsql
AS $function$
/*
 * 功能：  仓库入库（本厂产品）
 * 描述：  
 * 时间：  2024/07
 * 开发者：
 * 数据：  
 */
	declare 
		json_datas json;
		_user_id text;
		_user_no text;
		_user_name text;
		_host text;
	
		_bill_no text;
		_sn_no text;
		_part_no text;
		_invp_area_no text;
		_lot_no text;

		_err_msg_text text;
		_err_pg_detail text;
		_err_msg text;		
		res returntype;
	begin
		json_datas := json(datas);
		_user_no := json_datas->>'user_no';
		_bill_no := json_datas->>'bill_no';	
		_sn_no := json_datas->>'sn_no';

		select user_id,user_name into _user_id,_user_name from ss_user where user_no=_user_no;
	
		--insert into a_test_log values(datas,'wms_prod_factory_inbound',localtimestamp);

		select part_no,lot_no into _part_no,_lot_no from me_finish_io where me_finish_io_no=_bill_no and sn_no=_sn_no;
		select invp_area_no into _invp_area_no from pd_part where part_no=_part_no;
		if coalesce(_invp_area_no, '')='' then
			res := row('false', '此物料无仓库资料，不能入库。');
			return to_json(res);
		end if;
	
		if not exists(select 1 from me_finish_io a where me_finish_io_no=_bill_no and sn_no=_sn_no) then
			_err_msg := format('此产品(标签：【%s】)不属于生产入库单【%s】，不能入库。', _sn_no,_bill_no);
			res := row('false', _err_msg);
			return to_json(res);
		end if;
	
		if not exists(select 1 from qm_si_lot_h a left join qm_si_lot_b_sn b on b.si_lot_h_id=a.si_lot_h_id 
			where a.move_order_no=_bill_no and a.si_conclusion_name in ('合格','特采') and b.sn_no=_sn_no) then
			res := row('false', '此产品未检验合格，不能入库');
			return to_json(res);
		end if;
		
		if exists(select 1 from me_finish_io WHERE me_finish_io_no=_bill_no and sn_no=_sn_no and me_finish_io_rmk4='入库完成') then
			res := row('false', '此产品已经入库，不能二次入库。');
			return to_json(res);
		end if;

		if not exists(select 1 from wm_sn where sn_no=_sn_no and sn_status='110') then
			res := row('false', '此产品标签状态不正确，不能入库。');
			return to_json(res);
		end if;
	
		update public.wm_sn set sn_status='800',sn_status_name='在库',invp_area_no=_invp_area_no,inventory_lot=_lot_no,
			upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
		where sn_no=_sn_no;

		update public.wm_sn set sn_status='800',sn_status_name='在库',invp_area_no=_invp_area_no,
			upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
		where sn_pack_50=_sn_no;

		update public.me_finish_io set me_finish_io_rmk4='入库完成',
			upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
		where me_finish_io_no=_bill_no and sn_no=_sn_no;

		if not exists(select distinct 1 from me_finish_io where me_finish_io_no=_bill_no and coalesce(me_finish_io_rmk4,'')='') then
			update public.me_finish_io_h set me_finish_io_rmk4='入库完成',
				upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
			where me_finish_io_no=_bill_no;
		end if;
	
		res := row('true', '仓库入库完成');
		return to_json(res);
	
	EXCEPTION WHEN OTHERS THEN 
		GET STACKED DIAGNOSTICS 
			_err_msg_text = MESSAGE_TEXT,
			_err_pg_detail = PG_EXCEPTION_DETAIL;
	
		_err_msg := format('错误信息:%s,详情:%s',_err_msg_text,_err_msg_text);
		res := row('false',_err_msg);
		return to_json(res);		
	END;
$function$
;
```




```sql
-- DROP FUNCTION public.af_pda_wms_prod_temp_receipt(varchar);

CREATE OR REPLACE FUNCTION public.af_pda_wms_prod_temp_receipt(datas character varying)
 RETURNS character varying
 LANGUAGE plpgsql
AS $function$
/*
 * 功能：  仓库暂收入库（分厂及委外厂产品）
 * 描述：  分厂及委外厂仅作为一个车间，直接使用生产完工入库单。
 * 时间：  2024/07
 * 开发者：
 * 数据：
 */
	declare
		json_datas json;
		_user_id text;
		_user_no text;
		_user_name text;
		_host text;

		_bill_no text;
		_bill_id text;

		_sn_no text;
		_temp_receipt_no text;
		_temp_receipt_id text;

		_part_no text;
		_mo_no text;
		_part_qty numeric;
		_part_qty_plan numeric;
		_part_qty_real numeric;

		_part_no_bill text;
		json_result json;

		_err_msg_text text;
		_err_pg_detail text;
		_err_msg text;
		res returntype;
	begin
		json_datas := json(datas);
		_user_no := json_datas->>'user_no';
		_bill_no := json_datas->>'bill_no';
		_sn_no := json_datas->>'sn_no';

		insert into a_test_log values(datas,'wms_temp_receipt',localtimestamp);

		-----------2024/09/25 添加同产品多入库单统一暂收 ------------------------------------

		if exists(select 1 from wm_temp_receipt where delivery_no=_bill_no) then
			select part_no into _part_no_bill from me_finish_io_h where me_finish_io_no=_bill_no;
			if not exists(select distinct 1 from me_finish_io where sn_no=_sn_no and part_no=_part_no_bill) then
				res := row('false', '扫描产品与单据不一致。');
				return to_json(res);
			end if;
			select me_finish_io_no into _bill_no from me_finish_io where sn_no=_sn_no and coalesce(me_finish_io_rmk4,'')='';
			if not exists(select 1 from wm_temp_receipt where delivery_no=_bill_no) then
				res := row('false', '此标签对应入库单没有暂收。');
				return to_json(res);
			end if;
		end if;

		-----------2024/09/25 end--------------------------------------------------------------

		select user_id,user_name into _user_id,_user_name from ss_user where user_no=_user_no;
		select me_finish_io_id_h into _bill_id from me_finish_io_h where me_finish_io_no=_bill_no;
		if not exists(select 1 from me_finish_io where me_finish_io_no=_bill_no and sn_no=_sn_no) then
			_err_msg := format('扫描条码【%s】不属于分厂发货入库申请单【%s】，不能暂收。', _sn_no,_bill_no);
			res := row('false', _err_msg);
			return to_json(res);
		end if;

		if not exists(select 1 from qm_si_lot_h a left join qm_si_lot_b_sn b on b.si_lot_h_id=a.si_lot_h_id
			where a.move_order_no=_bill_no and a.si_conclusion_name='合格' and b.sn_no=_sn_no and si_lot_move_type='310') then
			res := row('false', '此产品未完工检验合格，不能暂收入库');
			return to_json(res);
		end if;

		if exists(select 1 from wm_temp_receipt a left join wm_temp_receipt_sn_part b on b.temp_receipt_id=a.temp_receipt_id
				where a.delivery_no=_bill_no and b.sn_no=_sn_no) then
			_err_msg := format('此条码【%s】已经暂收，不能二次暂收。', _sn_no);
			res := row('false', _err_msg);
			return to_json(res);
		end if;

		select part_no,mo_no,finish_io_qty_ok into _part_no,_mo_no,_part_qty from me_finish_io where sn_no=_sn_no and coalesce(me_finish_io_rmk4,'')='';
		----------------------------------------------------------------------------------------------------------------

		if not exists(select 1 from wm_temp_receipt where delivery_no=_bill_no) then
			--
			_temp_receipt_id := af_auid();

			insert into public.wm_temp_receipt
			(temp_receipt_id, temp_receipt_status, temp_receipt_no, temp_receipt_type, delivery_no, factory_no, temp_receipt_rmk01, temp_receipt_rmk02, temp_receipt_rmk03, temp_receipt_rmk04, temp_receipt_rmk05, temp_receipt_rmk06, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host)
			values(_temp_receipt_id, '110', af_ss_no_generate('wm_temp_receipt_no'), '', _bill_no, 'comlink', '', '', '', '', '', '', localtimestamp, _user_id, _user_no, _user_name, '', localtimestamp, _user_id, _user_no, _user_name, '');

			--select dlv_id into _bill_id from me_dlv where dlv_no=_bill_no;
			insert into public.wm_temp_receipt_b
			(temp_receipt_b_id, temp_receipt_id, part_no, part_name, part_spec, part_unit, part_qty_plan, part_qty_real, mo_no, temp_receipt_rmk01, temp_receipt_rmk02, temp_receipt_rmk03, temp_receipt_rmk04, temp_receipt_rmk05, temp_receipt_rmk06, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host)
			select af_auid(), _temp_receipt_id, b.part_no, c.part_name, c.part_spec, c.part_unit, a.finish_io_qty_ok, 0, a.mo_no, '', '', '', '', '', '', localtimestamp, _user_id, _user_no, _user_name, '', localtimestamp, _user_id, _user_no, _user_name, ''
			from me_finish_io_h a
			left join mo b on b.mo_no=a.mo_no
			left join pd_part c on c.part_no=b.part_no
			where me_finish_io_no=_bill_no;
		end if;

		select temp_receipt_id,temp_receipt_no into _temp_receipt_id,_temp_receipt_no from wm_temp_receipt where delivery_no=_bill_no;

		select part_qty_plan,part_qty_real into _part_qty_plan,_part_qty_real
		from wm_temp_receipt_b where temp_receipt_id=_temp_receipt_id and part_no=_part_no and mo_no=_mo_no;

		if _part_qty_plan < _part_qty_real+_part_qty then
			_err_msg := format('实际暂收数量(已暂收【%s】+现条码【%s】)大于计划(发货单)数量【%s】。', _part_qty_real::int,_part_qty::int,_part_qty_plan::int);
			res := row('false', _err_msg);
			return to_json(res);
		end if;

		if _part_qty_plan = _part_qty_real+_part_qty then
			update public.wm_temp_receipt_b set part_qty_real=part_qty_real+_part_qty,temp_receipt_rmk01='暂收完成',upd_time=localtimestamp
			where temp_receipt_id=_temp_receipt_id and part_no=_part_no and mo_no=_mo_no;
		else
			update public.wm_temp_receipt_b set part_qty_real=part_qty_real+_part_qty,upd_time=localtimestamp
			where temp_receipt_id=_temp_receipt_id and part_no=_part_no and mo_no=_mo_no;
		end if;

		insert into public.wm_temp_receipt_sn_part
		(temp_receipt_sn_id, temp_receipt_id, sn_no, part_no, part_name, part_spec, part_unit, part_qty, mo_no, temp_receipt_rmk01, temp_receipt_rmk02, temp_receipt_rmk03, temp_receipt_rmk04, temp_receipt_rmk05, temp_receipt_rmk06, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host)
		select af_auid(), _temp_receipt_id, _sn_no, _part_no, part_name, part_spec, part_unit, _part_qty, _mo_no, '', '', '', '', '', '', localtimestamp, _user_id, _user_no, _user_name, '', localtimestamp, _user_id, _user_no, _user_name, ''
		from pd_part
		where part_no=_part_no;

		---------------------------------------------------------------------------------------------------------------
		update public.wm_sn set sn_status='840',sn_status_name='暂收',invp_no='',invp_area_no='暂收库',
			upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
		where sn_no =_sn_no;

		update public.me_finish_io set me_finish_io_rmk4 ='暂收完成',upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
		where me_finish_io_no=_bill_no and sn_no=_sn_no;

		if not exists(select distinct 1 from me_finish_io where me_finish_io_no=_bill_no and coalesce(me_finish_io_rmk4,'')<>'暂收完成') then
			update public.me_finish_io_h set me_finish_io_rmk4 ='暂收完成',upd_time=localtimestamp
			where me_finish_io_no=_bill_no;
		end if;
		---------------------------------------------------------------------------------------------------------------
		if not exists(select distinct 1 from wm_temp_receipt_b where temp_receipt_id=_temp_receipt_id and temp_receipt_rmk01='') then
			update public.wm_temp_receipt set temp_receipt_rmk01='暂收完成'
			where temp_receipt_id=_temp_receipt_id;
		end if;

		if exists(select 1 from wm_temp_receipt where temp_receipt_id=_temp_receipt_id and coalesce(temp_receipt_rmk01,'')='暂收完成') then
			json_result := af_ax_wms_temp_receipt_push_qm_si_lot(json_build_object('user_no',_user_no,'host','PDA','selected',json_agg(json_build_object('temp_receipt_no',_temp_receipt_no,'temp_receipt_id',_temp_receipt_id)))::text);
			if json_result->>'successful'='false' then
				res := row('false', json_result->>'msg');
				return to_json(res);
			end if;
		end if;

		res := row('true', '仓库暂收完成');
		return to_json(res);

	EXCEPTION WHEN OTHERS THEN
		GET STACKED DIAGNOSTICS
			_err_msg_text = MESSAGE_TEXT,
			_err_pg_detail = PG_EXCEPTION_DETAIL;

		_err_msg := format('错误信息:%s,详情:%s',_err_msg_text,_err_msg_text);
		res := row('false',_err_msg);
		return to_json(res);
	END;
$function$
;

```


```sql
-- DROP FUNCTION public.af_ax_wms_temp_receipt_push_qm_si_lot(varchar);

CREATE OR REPLACE FUNCTION public.af_ax_wms_temp_receipt_push_qm_si_lot(datas character varying)
 RETURNS character varying
 LANGUAGE plpgsql
AS $function$
/*
 * 功能：  暂收单下推品质检验单
 * 描述：
 * 时间：
 * 开发者：
 */
	declare 
		json_datas json;
		_user_id text;
		_user_no text;
		_user_name text;
		_host text;

		_temp_receipt_no text;
		_temp_receipt_id text;
		_qm_si_lot_no text;
		_qm_si_lot_id text;
	
		_part_qty numeric;
		
		row_datas record;
		_client_no text;
		_client_name text;
	
		_err_msg_text text;
		_err_pg_detail text;
		_err_msg text;			
		res returntype;	
	
	begin
		json_datas := json(datas);
		_user_no := json_datas->>'user_no';
		_host := json_datas->>'host';
		
		json_datas := json(json_datas->'selected'->0);
		_temp_receipt_no := json_datas->>'temp_receipt_no';
		_temp_receipt_id := json_datas->>'temp_receipt_id';

		--insert into a_test_log values(datas, 'push_si_lot', localtimestamp);
	
		select user_id,user_name into _user_id,_user_name from ss_user where user_no=_user_no;
	
		if exists(select 1 from qm_si_lot_h where move_order_no=_temp_receipt_no) then
			res := row('false', 此暂收单已经下推检验单，不能二次下推);
			return to_json(res);
		end if;
	
		----------------------------------------------------------------------------------------------------------------
		
		for row_datas in (select temp_receipt_b_id,temp_receipt_id,part_no,mo_no from wm_temp_receipt_b where temp_receipt_id=_temp_receipt_id) loop
		
			_qm_si_lot_id := af_auid();

			select client_no,client_name into _client_no,_client_name from mo where mo_no=row_datas.mo_no;
		
			insert into public.qm_si_lot_h
			(si_lot_h_id, si_lot_h_no, si_lot_h_status, factory_no, factory_name, part_no, part_name, part_spec, part_idt, wkp_no, wkp_name, si_lot_qty, si_lot_move_type, si_lot_move_type_name, move_order_h_id, move_order_b_id, move_order_id, move_order_no, order_type, order_type_name, order_h_id, order_b_id, order_id, order_no, client_no, client_name, supplier_no, supplier_name, si_type, si_type_name, si_degree, si_degree_name, si_level, si_level_name, si_aql, si_lot_qty_ok, si_lot_qty_ng, si_conclusion_no, si_conclusion_name, si_is_pass, si_lot_h_rmk01, si_lot_h_rmk02, si_lot_h_rmk03, si_lot_h_rmk04, si_lot_h_rmk05, si_lot_h_rmk06, si_lot_h_rmk07, si_lot_h_rmk08, si_lot_h_rmk09, si_lot_h_rmk10, da_switch_id, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host, io_is_sucessed, io_times, io_last_time, delivery_order_no, si_sample_qty, si_lot_h_rmk11, si_lot_h_rmk12, si_lot_h_rmk13, si_lot_h_rmk14, si_lot_h_rmk15, si_lot_h_rmk16, si_lot_h_rmk17, si_lot_h_rmk18, si_lot_h_rmk19, si_lot_h_rmk20, si_lot_h_rmk21, si_lot_h_rmk22, si_lot_h_rmk23, si_lot_h_rmk24, si_lot_h_rmk25, si_lot_h_rmk26, si_lot_h_rmk27, si_lot_h_rmk28, si_lot_h_rmk29, si_lot_h_rmk30, si_lot_h_rmk31, price, priceafvat, lot_no, put_qty, si_lot_h_rmk32, whscode, canceled, qm_si_gist_no, ea_no, ea_name, si_lot_h_rmk33, si_lot_h_rmk34)
			select _qm_si_lot_id, af_ss_no_generate('wgjy_si_lot_h_no'), '5B8A5FD43EEB00004DFE', 'comlink', '深圳市金洋电子股份有限公司', part_no, part_name, coalesce(part_spec,''), '', '', '', part_qty_real, '315', '完工检验(复检)', _temp_receipt_id, '', _temp_receipt_id, _temp_receipt_no, 'MO', '生产订单', '', '', '', row_datas.mo_no, _client_no, _client_name, '', '', '20', '抽检', '20', '正常', '10', '一般', '', 0, 0, '', '', false, '', '', '', '', '', '', '', '', '', '', '', localtimestamp, _user_id, _user_no, _user_name, '', localtimestamp, _user_id, _user_no, _user_name, '', false, 0, null, '', 0, '', '', '', '', false, '', localtimestamp, null, '', '', '', '', false, '', '', '', '', '', '', 0, '', 0, 0, '', 0, '', '', '', '', '', '', '', ''
			from wm_temp_receipt_b
			where temp_receipt_b_id=row_datas.temp_receipt_b_id;

			insert into public.qm_si_lot_b_sn
			(si_lot_b_sn_id, si_lot_h_id, si_lot_b_id, sn_no, si_conclusion, qa_cause_no, qa_cause_name, si_lot_b_sn_rmk01, si_lot_b_sn_rmk02, si_lot_b_sn_rmk03, si_lot_b_sn_rmk04, si_lot_b_sn_rmk05, si_lot_b_sn_rmk06, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host, sn_pack_20, sn_pack_30, sn_pack_40, sn_pack_50, part_qty, mo_no, weight_gross, weight_net, pack_qty_used, ea_no, ea_name, sn_type, sn_type_name)
			select af_auid(), _qm_si_lot_id, '', a.sn_no, '', '', '', '', '', '', '', '', '', localtimestamp, _user_id, _user_no, _user_name, '', localtimestamp, _user_id, _user_no, _user_name, '', '20', '', '', '', a.part_qty, b.mo_no, b.weight_gross, b.weight_net, pack_qty_used, b.ea_no, b.ea_name, b.sn_type, b.sn_type_name
			from wm_temp_receipt_sn_part a
			left join wm_sn b on b.sn_no=a.sn_no
			where temp_receipt_id=row_datas.temp_receipt_id and a.part_no=row_datas.part_no and a.mo_no=row_datas.mo_no;
	
		end loop;
	

		update public.wm_temp_receipt set temp_receipt_rmk01='下推检验单完成',push_time=localtimestamp,upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
		where temp_receipt_no=_temp_receipt_no;
	
		res := row('true', '下推品质检验单完成');
		return to_json(res);
	
	EXCEPTION WHEN OTHERS THEN 
		GET STACKED DIAGNOSTICS 
			_err_msg_text = MESSAGE_TEXT,
			_err_pg_detail = PG_EXCEPTION_DETAIL;
	
		_err_msg := format('错误信息:%s,详情:%s',_err_msg_text,_err_msg_text);
		res := row('false',_err_msg);
		return to_json(res);			
	end;

$function$
;


```

---

## **PDA仓库接收入库系统分析报告**

### **1. 系统概述**

这是一个完整的PDA仓库接收入库管理系统，由四个核心函数组成，实现了多组织、多场景的生产入库全流程管理。该系统支持本厂产品直接入库和分厂/委外厂产品暂收入库两种模式，并集成了质量检验流程。

#### **业务场景**
- **使用场景**: 仓库人员使用PDA设备扫描生产完工产品，完成入库确认
- **核心价值**: 支持多组织生产模式、确保质量检验合格、实现库存状态准确管理
- **适用行业**: 多工厂协同生产的制造企业、委外加工管理

#### **四函数架构体系**
1. **主控分发函数** (`af_pda_wms_prod_inbound`)
   - 统一入口，根据组织类型智能分发处理流程
   - 支持本厂、江西分厂等不同组织的差异化处理

2. **本厂入库函数** (`af_pda_wms_prod_factory_inbound`)
   - 处理本厂生产的产品直接入库
   - 完整的质量检验和状态流转管理

3. **暂收入库函数** (`af_pda_wms_prod_temp_receipt`)
   - 处理分厂/委外厂产品的暂收入库
   - 支持多入库单统一暂收的复杂业务场景

4. **检验单下推函数** (`af_ax_wms_temp_receipt_push_qm_si_lot`)
   - 暂收完成后自动生成质量检验单
   - 实现暂收到检验的无缝衔接

---

## **2. 数据逻辑分析**

### **2.1 数据流向架构图**

```mermaid
graph TD
    A[PDA扫描输入] --> B[主控函数]
    B --> C{组织类型判断}

    C -->|本厂workshop_no| D[本厂入库流程]
    C -->|江西分厂JX开头| E[暂收入库流程]

    D --> F[质量检验校验]
    E --> G[暂收单管理]

    F --> H[直接入库]
    G --> I[暂收完成检查]

    H --> J[库存状态更新]
    I --> K{是否全部暂收完成}

    K -->|是| L[自动下推检验单]
    K -->|否| M[等待后续暂收]

    L --> N[生成检验批]
    J --> O[完成入库]
    N --> O
    M --> O

    subgraph "数据校验层"
        P[入库单校验]
        Q[条码状态校验]
        R[质检结果校验]
        S[重复入库校验]
    end

    B --> P
    D --> Q
    D --> R
    D --> S
    E --> Q
    E --> R
    E --> S
```

![1751454824619](D:\金洋\成品出入库记录\入库\assets\1751454824619.png)

### **2.2 核心数据实体关系**

| 表名 | 作用 | 关键字段 | 数据流向 |
|------|------|----------|----------|
| `me_finish_io_h` | 完工单头 | `me_finish_io_no`, `workshop_no`, `me_finish_io_rmk4` | 主控制表 |
| `me_finish_io` | 完工单明细 | `sn_no`, `part_no`, `lot_no`, `me_finish_io_rmk4` | 明细控制 |
| `wm_sn` | 条码主数据 | `sn_no`, `sn_status`, `sn_pack_50` | 库存状态管理 |
| `qm_si_lot_h` | 检验批头 | `move_order_no`, `si_conclusion_name`, `si_lot_move_type` | 质量控制 |
| `qm_si_lot_b_sn` | 检验批明细 | `sn_no`, `si_conclusion` | 质量追踪 |
| `wm_temp_receipt` | 暂收单头 | `delivery_no`, `temp_receipt_status`, `temp_receipt_rmk01` | 暂收管理 |
| `wm_temp_receipt_b` | 暂收单身 | `part_qty_plan`, `part_qty_real`, `temp_receipt_rmk01` | 数量控制 |
| `wm_temp_receipt_sn_part` | 暂收序列号 | `sn_no`, `temp_receipt_id` | 序列号追踪 |
| `pd_part` | 物料主数据 | `part_no`, `invp_area_no` | 仓库分配 |

### **2.3 组织类型识别逻辑**

#### **组织代码映射规则**
```sql
-- 组织类型识别和转换
select (case when workshop_no='J129' then 'JXML'
             when workshop_no='J171' then 'JXRS'
             else workshop_no end) into _inbound_org
from me_finish_io_h where me_finish_io_no=_bill_no;

-- 处理流程分发
if strpos(_inbound_org,'JX')=1 then
    -- 江西分厂：暂收流程
    json_result := af_pda_wms_prod_temp_receipt(...)
else
    -- 本厂：直接入库流程
    json_result := af_pda_wms_prod_factory_inbound(...)
end if;
```

#### **数据状态流转对比**

| 处理模式 | 条码状态流转 | 完工单状态 | 最终库存状态 |
|----------|-------------|------------|-------------|
| **本厂入库** | `110(确认)` → `800(在库)` | `''` → `入库完成` | 正式库存 |
| **暂收入库** | `110(确认)` → `840(暂收)` | `''` → `暂收完成` | 暂收库存 |

### **2.4 数据完整性控制机制**

#### **质量检验控制逻辑**
```sql
-- 本厂产品质检校验
select 1 from qm_si_lot_h a
left join qm_si_lot_b_sn b on b.si_lot_h_id=a.si_lot_h_id
where a.move_order_no=_bill_no
  and a.si_conclusion_name in ('合格','特采')
  and b.sn_no=_sn_no

-- 分厂产品质检校验
select 1 from qm_si_lot_h a
left join qm_si_lot_b_sn b on b.si_lot_h_id=a.si_lot_h_id
where a.move_order_no=_bill_no
  and a.si_conclusion_name='合格'
  and b.sn_no=_sn_no
  and si_lot_move_type='310'
```

#### **数量平衡控制**
```sql
-- 暂收数量控制
if _part_qty_plan < _part_qty_real + _part_qty then
    -- 超出计划数量，拒绝暂收
end if;

-- 完成状态自动判断
if _part_qty_plan = _part_qty_real + _part_qty then
    -- 更新明细完成状态
    update wm_temp_receipt_b set temp_receipt_rmk01='暂收完成'
end if;
```

---

## **3. 业务逻辑分析**

### **3.1 多组织业务模式对比**

#### **本厂 vs 分厂业务差异分析**

| 对比维度 | 本厂模式 | 分厂/委外模式 |
|----------|----------|---------------|
| **业务性质** | 直接生产入库 | 跨组织协同入库 |
| **处理流程** | 一步到位入库 | 暂收→检验→正式入库 |
| **质检要求** | 合格或特采 | 仅合格 |
| **库存状态** | 直接在库(800) | 先暂收(840) |
| **检验类型** | 完工检验 | 完工检验(复检) |
| **单据流转** | 完工单→入库完成 | 完工单→暂收单→检验单 |
| **复杂度** | 简单 | 复杂 |

#### **业务规则矩阵**

| 业务环节 | 本厂规则 | 分厂规则 | 共同规则 |
|----------|----------|----------|----------|
| **组织识别** | 非JX开头 | JX开头 | workshop_no映射 |
| **质检标准** | 合格+特采 | 仅合格 | 必须检验合格 |
| **状态更新** | 直接在库 | 暂收状态 | 防重复处理 |
| **完成判断** | 明细全部入库 | 明细全部暂收 | 自动状态流转 |
| **后续流程** | 入库完成 | 下推检验单 | 状态同步更新 |

### **3.2 复杂业务场景深度解析**

#### **3.2.1 多入库单统一暂收逻辑**
```sql
-- 2024/09/25 新增的复杂业务逻辑
if exists(select 1 from wm_temp_receipt where delivery_no=_bill_no) then
    -- 检查产品一致性
    select part_no into _part_no_bill from me_finish_io_h where me_finish_io_no=_bill_no;

    -- 查找条码对应的未完成入库单
    select me_finish_io_no into _bill_no from me_finish_io
    where sn_no=_sn_no and coalesce(me_finish_io_rmk4,'')='';

    -- 验证暂收单存在性
    if not exists(select 1 from wm_temp_receipt where delivery_no=_bill_no) then
        res := row('false', '此标签对应入库单没有暂收。');
    end if;
end if;
```
**业务价值**: 支持同一产品多个入库单的统一暂收管理，提高操作灵活性

#### **3.2.2 外箱关联处理逻辑**
```sql
-- 本厂入库的外箱处理
update public.wm_sn set sn_status='800',sn_status_name='在库',
    invp_area_no=_invp_area_no,inventory_lot=_lot_no
where sn_pack_50=_sn_no;  -- 更新外箱内的所有商品
```
**业务价值**: 扫描外箱时自动更新内包装状态，提高操作效率

#### **3.2.3 自动检验单下推逻辑**
```sql
-- 暂收完成后自动下推检验单
if exists(select 1 from wm_temp_receipt
          where temp_receipt_id=_temp_receipt_id
          and coalesce(temp_receipt_rmk01,'')='暂收完成') then
    json_result := af_ax_wms_temp_receipt_push_qm_si_lot(...)
end if;
```
**业务价值**: 实现暂收到检验的自动化流转，减少人工干预

### **3.3 业务流程时序图**

```mermaid
sequenceDiagram
    participant User as 仓库操作员
    participant PDA as PDA设备
    participant Main as 主控系统
    participant Local as 本厂系统
    participant Temp as 暂收系统
    participant QM as 质检系统

    User->>PDA: 扫描入库单号+产品条码
    PDA->>Main: 调用主控函数
    Main->>Main: 识别组织类型

    alt 本厂产品
        Main->>Local: 调用本厂入库函数
        Local->>Local: 质检结果校验
        Local->>Local: 更新库存状态(在库)
        Local->>Local: 更新完工单状态
        Local-->>Main: 返回入库完成
    else 分厂产品
        Main->>Temp: 调用暂收函数
        Temp->>Temp: 创建/更新暂收单
        Temp->>Temp: 更新库存状态(暂收)

        alt 暂收完成
            Temp->>QM: 自动下推检验单
            QM->>QM: 生成检验批
            QM-->>Temp: 返回下推成功
        end

        Temp-->>Main: 返回暂收完成
    end

    Main-->>PDA: 返回处理结果
    PDA-->>User: 显示操作结果
```

---

## **4. 代码逻辑分析**

### **4.1 代码架构设计模式**

#### **策略模式 + 工厂模式**
```sql
-- 主控函数：工厂模式选择处理策略
if strpos(_inbound_org,'JX')=1 then
    json_result := af_pda_wms_prod_temp_receipt(...)  -- 暂收策略
else
    json_result := af_pda_wms_prod_factory_inbound(...) -- 直接入库策略
end if;
```

#### **模板方法模式**
```
通用处理模板：
1. JSON数据解析
2. 用户信息获取
3. 业务数据校验
4. 核心业务处理
5. 状态更新操作
6. 完成度检查
7. 异常处理机制
```

### **4.2 关键代码逻辑解析**

#### **4.2.1 动态组织识别逻辑**
```sql
-- 复杂的组织代码映射
select (case when workshop_no='J129' then 'JXML'
             when workshop_no='J171' then 'JXRS'
             else workshop_no end) into _inbound_org
```

#### **4.2.2 条件化单据创建逻辑**
```sql
-- 暂收单的条件创建
if not exists(select 1 from wm_temp_receipt where delivery_no=_bill_no) then
    -- 创建暂收单头
    insert into wm_temp_receipt (...)
    -- 创建暂收单身
    insert into wm_temp_receipt_b (...)
end if;
```

#### **4.2.3 批量状态更新策略**
```sql
-- 本厂入库：同时更新条码和外箱
update wm_sn set sn_status='800' where sn_no=_sn_no;
update wm_sn set sn_status='800' where sn_pack_50=_sn_no;

-- 暂收入库：仅更新条码状态
update wm_sn set sn_status='840' where sn_no=_sn_no;
```

### **4.3 代码质量分析**

#### **优点**
- ✅ **架构清晰**: 主控+策略的分层设计
- ✅ **业务完整**: 覆盖多组织复杂业务场景
- ✅ **扩展性好**: 易于增加新的组织类型
- ✅ **自动化程度高**: 状态流转和检验单下推自动化

#### **潜在改进点**
- ⚠️ **硬编码组织**: 组织映射规则硬编码在代码中
- ⚠️ **函数复杂度**: 暂收函数逻辑较为复杂
- ⚠️ **错误处理**: 部分业务异常处理可以更细化
- ⚠️ **性能优化**: 存在重复查询的情况

#### **代码优化建议**
```sql
-- 建议创建组织配置表
CREATE TABLE org_config (
    workshop_no varchar,
    org_code varchar,
    process_type varchar  -- 'DIRECT' or 'TEMP_RECEIPT'
);

-- 优化组织识别逻辑
select org_code, process_type into _inbound_org, _process_type
from org_config where workshop_no = _workshop_no;
```

### **4.4 异常处理机制分析**

#### **分层异常处理**
```sql
-- 业务异常：主动校验和返回
if not exists(质检合格记录) then
    res := row('false', '此产品未检验合格，不能入库');
    return to_json(res);
end if;

-- 系统异常：统一捕获处理
EXCEPTION WHEN OTHERS THEN
    GET STACKED DIAGNOSTICS
        _err_msg_text = MESSAGE_TEXT,
        _err_pg_detail = PG_EXCEPTION_DETAIL;
```

#### **错误信息标准化**
- **格式统一**: 使用format函数格式化错误信息
- **信息完整**: 包含关键业务数据便于定位问题
- **分类清晰**: 区分业务错误和系统错误

---

## **5. 表关联逻辑分析**

### **5.1 核心表关联关系图**

```mermaid
erDiagram
    ME_FINISH_IO_H ||--o{ ME_FINISH_IO : "完工单头身关联"
    ME_FINISH_IO ||--o{ WM_SN : "条码关联"
    ME_FINISH_IO ||--o{ QM_SI_LOT_B_SN : "质检关联"
    QM_SI_LOT_H ||--o{ QM_SI_LOT_B_SN : "检验批关联"

    WM_TEMP_RECEIPT ||--o{ WM_TEMP_RECEIPT_B : "暂收单头身关联"
    WM_TEMP_RECEIPT ||--o{ WM_TEMP_RECEIPT_SN_PART : "暂收序列号关联"
    WM_TEMP_RECEIPT_B ||--o{ WM_TEMP_RECEIPT_SN_PART : "暂收明细关联"

    PD_PART ||--o{ ME_FINISH_IO : "物料关联"
    PD_PART ||--o{ WM_SN : "物料关联"
    MO ||--o{ ME_FINISH_IO : "工单关联"

    ME_FINISH_IO_H {
        varchar me_finish_io_no PK "完工单号"
        varchar workshop_no "车间代码"
        varchar me_finish_io_rmk4 "入库状态"
        varchar me_finish_io_id_h "完工单头ID"
    }

    ME_FINISH_IO {
        varchar me_finish_io_no FK "完工单号"
        varchar sn_no FK "序列号"
        varchar part_no FK "物料编码"
        varchar lot_no "批次号"
        varchar me_finish_io_rmk4 "入库状态"
        numeric finish_io_qty_ok "合格数量"
    }

    WM_SN {
        varchar sn_no PK "序列号"
        varchar sn_status "状态代码"
        varchar sn_status_name "状态名称"
        varchar invp_area_no "库区代码"
        varchar inventory_lot "库存批次"
        varchar sn_pack_50 "外箱关联"
    }

    WM_TEMP_RECEIPT {
        varchar temp_receipt_id PK "暂收单ID"
        varchar temp_receipt_no "暂收单号"
        varchar delivery_no FK "发货单号"
        varchar temp_receipt_rmk01 "暂收状态"
        timestamp push_time "下推时间"
    }
```

![1751454971918](D:\金洋\成品出入库记录\入库\assets\1751454971918.png)



### *5.2 表关联逻辑详解**

#### **5.2.1 本厂入库关联逻辑**
```sql
-- 完工单明细关联
select part_no,lot_no into _part_no,_lot_no
from me_finish_io
where me_finish_io_no=_bill_no and sn_no=_sn_no;

-- 物料仓库关联
select invp_area_no into _invp_area_no
from pd_part where part_no=_part_no;

-- 质检结果关联
select 1 from qm_si_lot_h a
left join qm_si_lot_b_sn b on b.si_lot_h_id=a.si_lot_h_id
where a.move_order_no=_bill_no and b.sn_no=_sn_no;
```

#### **5.2.2 暂收入库关联逻辑**
```sql
-- 暂收单创建关联
insert into wm_temp_receipt_b (...)
select ..., a.finish_io_qty_ok, 0, a.mo_no, ...
from me_finish_io_h a
left join mo b on b.mo_no=a.mo_no
left join pd_part c on c.part_no=b.part_no
where me_finish_io_no=_bill_no;

-- 序列号明细关联
insert into wm_temp_receipt_sn_part (...)
select ..., _sn_no, _part_no, part_name, ...
from pd_part where part_no=_part_no;
```

#### **5.2.3 检验单下推关联逻辑**
```sql
-- 检验批头创建
insert into qm_si_lot_h (...)
select ..., part_no, part_name, part_qty_real, ...
from wm_temp_receipt_b
where temp_receipt_b_id=row_datas.temp_receipt_b_id;

-- 检验批明细创建
insert into qm_si_lot_b_sn (...)
select ..., a.sn_no, a.part_qty, b.mo_no, ...
from wm_temp_receipt_sn_part a
left join wm_sn b on b.sn_no=a.sn_no;
```

### **5.3 数据一致性保证机制**

#### **状态同步机制**
- **条码状态**: 根据业务模式更新为在库或暂收状态
- **完工单状态**: 明细和单头状态的级联更新
- **暂收单状态**: 明细完成后自动更新单头状态

#### **数据完整性约束**
- **外键关系**: 通过ID关联保证数据完整性
- **业务约束**: 通过状态字段控制业务流程
- **数量平衡**: 计划数量与实际数量的平衡控制

---

## **6. 系统优化建议**

### **6.1 架构层面优化**
- 🏗️ **配置化管理**: 将组织映射规则移到配置表管理
- 🏗️ **服务拆分**: 将检验单下推独立为微服务
- 🏗️ **事务优化**: 增加分布式事务控制机制
- 🏗️ **接口标准化**: 统一函数接口和返回格式

### **6.2 性能优化**
- ⚡ **查询优化**: 减少重复查询，使用临时变量缓存
- ⚡ **索引优化**: 在关联字段和状态字段上创建复合索引
- ⚡ **批量处理**: 支持批量扫描和处理
- ⚡ **异步处理**: 检验单下推采用异步处理

### **6.3 功能增强**
- 📈 **监控告警**: 增加业务异常监控和告警
- 📈 **数据分析**: 增加入库效率和质量分析
- 📈 **移动优化**: 优化PDA操作界面和流程
- 📈 **集成扩展**: 支持更多第三方系统集成

### **6.4 业务扩展**
- 🎯 **组织扩展**: 支持更多组织类型和处理模式
- 🎯 **质检集成**: 与质检设备和系统深度集成
- 🎯 **成本核算**: 集成成本核算和财务处理
- 🎯 **供应链协同**: 扩展供应商协同功能

---

## **总结**

这个PDA仓库接收入库系统是一个设计完善的多组织生产入库解决方案，具有以下特点：

### **技术特色**
- **多模式支持**: 本厂直接入库和分厂暂收入库双模式
- **智能分发**: 根据组织类型自动选择处理流程
- **自动化集成**: 暂收完成自动下推检验单
- **状态管理完整**: 从条码到单据的全链路状态管理

### **业务价值**
- **多组织协同**: 支持本厂、分厂、委外厂的统一管理
- **质量保证**: 严格的质检合格校验机制
- **流程自动化**: 减少人工干预，提高操作效率
- **数据准确性**: 多重校验确保库存数据准确

### **应用场景**
- 多工厂协同生产的制造企业
- 委外加工业务管理
- 跨组织质量管控
- 复杂供应链库存管理

### **核心优势**
- **架构灵活**: 易于扩展新的组织类型和业务模式
- **业务完整**: 覆盖了多组织入库的完整业务流程
- **集成度高**: 与质检、库存、财务系统深度集成
- **操作便捷**: PDA扫描操作简单高效

该系统为多组织协同生产企业提供了完整的入库管理数字化解决方案，是现代制造业供应链管理的重要基础设施。
