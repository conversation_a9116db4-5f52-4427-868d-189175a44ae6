```sql
-- DROP FUNCTION public.af_pda_wms_prod_inbound(varchar);

CREATE OR REPLACE FUNCTION public.af_pda_wms_prod_inbound(datas character varying)
 RETURNS character varying
 LANGUAGE plpgsql
AS $function$
/*
 * 功能：  仓库入库（本厂，分厂及委外厂产品）
 * 描述：  
 * 时间：  2024/07
 * 开发者：
 * 数据：  
 */
	declare 
		json_datas json;
		_user_id text;
		_user_no text;
		_user_name text;
		_host text;
	
		_bill_no text;
		_sn_no text;
		_inbound_org text;
		json_result json;
	
		_err_msg_text text;
		_err_pg_detail text;
		_err_msg text;		
		res returntype;
	begin
		json_datas := json(datas);
		_user_no := json_datas->>'user_no';
	
		json_datas := json(json_datas->'datas'->0);
		_bill_no := json_datas->>'bill_no';		
		_sn_no := json_datas->>'sn_no';	
	
		--insert into a_test_log values(datas,'wms_prod_inbound',localtimestamp);
	
		--select me_finish_io_rmk1 into _inbound_org from me_finish_io_h where me_finish_io_no=_bill_no;

		select (case when workshop_no='J129' then 'JXML' when workshop_no='J171' then 'JXRS' else workshop_no end) into _inbound_org 
		from me_finish_io_h where me_finish_io_no=_bill_no;

		if coalesce(_inbound_org, '')='' then
			res := row('false', '此入库单没有指定入库组织，不能入库');
			return to_json(res);
		end if;
		
		if strpos(_inbound_org,'JX')=1 then
			json_result := af_pda_wms_prod_temp_receipt(json_build_object('user_no',_user_no,'bill_no',_bill_no,'sn_no',_sn_no)::text);
		else
			json_result := af_pda_wms_prod_factory_inbound(json_build_object('user_no',_user_no,'bill_no',_bill_no,'sn_no',_sn_no)::text);
		end if;
	
		res := row(json_result->>'successful', json_result->>'msg');
		return to_json(res);

	EXCEPTION WHEN OTHERS THEN 
		GET STACKED DIAGNOSTICS 
			_err_msg_text = MESSAGE_TEXT,
			_err_pg_detail = PG_EXCEPTION_DETAIL;
	
		_err_msg := format('错误信息:%s,详情:%s',_err_msg_text,_err_msg_text);
		res := row('false',_err_msg);
		return to_json(res);		

	END;
$function$
;

```



```sql
-- DROP FUNCTION public.af_pda_wms_prod_factory_inbound(varchar);

CREATE OR REPLACE FUNCTION public.af_pda_wms_prod_factory_inbound(datas character varying)
 RETURNS character varying
 LANGUAGE plpgsql
AS $function$
/*
 * 功能：  仓库入库（本厂产品）
 * 描述：  
 * 时间：  2024/07
 * 开发者：
 * 数据：  
 */
	declare 
		json_datas json;
		_user_id text;
		_user_no text;
		_user_name text;
		_host text;
	
		_bill_no text;
		_sn_no text;
		_part_no text;
		_invp_area_no text;
		_lot_no text;

		_err_msg_text text;
		_err_pg_detail text;
		_err_msg text;		
		res returntype;
	begin
		json_datas := json(datas);
		_user_no := json_datas->>'user_no';
		_bill_no := json_datas->>'bill_no';	
		_sn_no := json_datas->>'sn_no';

		select user_id,user_name into _user_id,_user_name from ss_user where user_no=_user_no;
	
		--insert into a_test_log values(datas,'wms_prod_factory_inbound',localtimestamp);

		select part_no,lot_no into _part_no,_lot_no from me_finish_io where me_finish_io_no=_bill_no and sn_no=_sn_no;
		select invp_area_no into _invp_area_no from pd_part where part_no=_part_no;
		if coalesce(_invp_area_no, '')='' then
			res := row('false', '此物料无仓库资料，不能入库。');
			return to_json(res);
		end if;
	
		if not exists(select 1 from me_finish_io a where me_finish_io_no=_bill_no and sn_no=_sn_no) then
			_err_msg := format('此产品(标签：【%s】)不属于生产入库单【%s】，不能入库。', _sn_no,_bill_no);
			res := row('false', _err_msg);
			return to_json(res);
		end if;
	
		if not exists(select 1 from qm_si_lot_h a left join qm_si_lot_b_sn b on b.si_lot_h_id=a.si_lot_h_id 
			where a.move_order_no=_bill_no and a.si_conclusion_name in ('合格','特采') and b.sn_no=_sn_no) then
			res := row('false', '此产品未检验合格，不能入库');
			return to_json(res);
		end if;
		
		if exists(select 1 from me_finish_io WHERE me_finish_io_no=_bill_no and sn_no=_sn_no and me_finish_io_rmk4='入库完成') then
			res := row('false', '此产品已经入库，不能二次入库。');
			return to_json(res);
		end if;

		if not exists(select 1 from wm_sn where sn_no=_sn_no and sn_status='110') then
			res := row('false', '此产品标签状态不正确，不能入库。');
			return to_json(res);
		end if;
	
		update public.wm_sn set sn_status='800',sn_status_name='在库',invp_area_no=_invp_area_no,inventory_lot=_lot_no,
			upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
		where sn_no=_sn_no;

		update public.wm_sn set sn_status='800',sn_status_name='在库',invp_area_no=_invp_area_no,
			upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
		where sn_pack_50=_sn_no;

		update public.me_finish_io set me_finish_io_rmk4='入库完成',
			upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
		where me_finish_io_no=_bill_no and sn_no=_sn_no;

		if not exists(select distinct 1 from me_finish_io where me_finish_io_no=_bill_no and coalesce(me_finish_io_rmk4,'')='') then
			update public.me_finish_io_h set me_finish_io_rmk4='入库完成',
				upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
			where me_finish_io_no=_bill_no;
		end if;
	
		res := row('true', '仓库入库完成');
		return to_json(res);
	
	EXCEPTION WHEN OTHERS THEN 
		GET STACKED DIAGNOSTICS 
			_err_msg_text = MESSAGE_TEXT,
			_err_pg_detail = PG_EXCEPTION_DETAIL;
	
		_err_msg := format('错误信息:%s,详情:%s',_err_msg_text,_err_msg_text);
		res := row('false',_err_msg);
		return to_json(res);		
	END;
$function$
;
```




```sql
-- DROP FUNCTION public.af_pda_wms_prod_temp_receipt(varchar);

CREATE OR REPLACE FUNCTION public.af_pda_wms_prod_temp_receipt(datas character varying)
 RETURNS character varying
 LANGUAGE plpgsql
AS $function$
/*
 * 功能：  仓库暂收入库（分厂及委外厂产品）
 * 描述：  分厂及委外厂仅作为一个车间，直接使用生产完工入库单。
 * 时间：  2024/07
 * 开发者：
 * 数据：
 */
	declare
		json_datas json;
		_user_id text;
		_user_no text;
		_user_name text;
		_host text;

		_bill_no text;
		_bill_id text;

		_sn_no text;
		_temp_receipt_no text;
		_temp_receipt_id text;

		_part_no text;
		_mo_no text;
		_part_qty numeric;
		_part_qty_plan numeric;
		_part_qty_real numeric;

		_part_no_bill text;
		json_result json;

		_err_msg_text text;
		_err_pg_detail text;
		_err_msg text;
		res returntype;
	begin
		json_datas := json(datas);
		_user_no := json_datas->>'user_no';
		_bill_no := json_datas->>'bill_no';
		_sn_no := json_datas->>'sn_no';

		insert into a_test_log values(datas,'wms_temp_receipt',localtimestamp);

		-----------2024/09/25 添加同产品多入库单统一暂收 ------------------------------------

		if exists(select 1 from wm_temp_receipt where delivery_no=_bill_no) then
			select part_no into _part_no_bill from me_finish_io_h where me_finish_io_no=_bill_no;
			if not exists(select distinct 1 from me_finish_io where sn_no=_sn_no and part_no=_part_no_bill) then
				res := row('false', '扫描产品与单据不一致。');
				return to_json(res);
			end if;
			select me_finish_io_no into _bill_no from me_finish_io where sn_no=_sn_no and coalesce(me_finish_io_rmk4,'')='';
			if not exists(select 1 from wm_temp_receipt where delivery_no=_bill_no) then
				res := row('false', '此标签对应入库单没有暂收。');
				return to_json(res);
			end if;
		end if;

		-----------2024/09/25 end--------------------------------------------------------------

		select user_id,user_name into _user_id,_user_name from ss_user where user_no=_user_no;
		select me_finish_io_id_h into _bill_id from me_finish_io_h where me_finish_io_no=_bill_no;
		if not exists(select 1 from me_finish_io where me_finish_io_no=_bill_no and sn_no=_sn_no) then
			_err_msg := format('扫描条码【%s】不属于分厂发货入库申请单【%s】，不能暂收。', _sn_no,_bill_no);
			res := row('false', _err_msg);
			return to_json(res);
		end if;

		if not exists(select 1 from qm_si_lot_h a left join qm_si_lot_b_sn b on b.si_lot_h_id=a.si_lot_h_id
			where a.move_order_no=_bill_no and a.si_conclusion_name='合格' and b.sn_no=_sn_no and si_lot_move_type='310') then
			res := row('false', '此产品未完工检验合格，不能暂收入库');
			return to_json(res);
		end if;

		if exists(select 1 from wm_temp_receipt a left join wm_temp_receipt_sn_part b on b.temp_receipt_id=a.temp_receipt_id
				where a.delivery_no=_bill_no and b.sn_no=_sn_no) then
			_err_msg := format('此条码【%s】已经暂收，不能二次暂收。', _sn_no);
			res := row('false', _err_msg);
			return to_json(res);
		end if;

		select part_no,mo_no,finish_io_qty_ok into _part_no,_mo_no,_part_qty from me_finish_io where sn_no=_sn_no and coalesce(me_finish_io_rmk4,'')='';
		----------------------------------------------------------------------------------------------------------------

		if not exists(select 1 from wm_temp_receipt where delivery_no=_bill_no) then
			--
			_temp_receipt_id := af_auid();

			insert into public.wm_temp_receipt
			(temp_receipt_id, temp_receipt_status, temp_receipt_no, temp_receipt_type, delivery_no, factory_no, temp_receipt_rmk01, temp_receipt_rmk02, temp_receipt_rmk03, temp_receipt_rmk04, temp_receipt_rmk05, temp_receipt_rmk06, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host)
			values(_temp_receipt_id, '110', af_ss_no_generate('wm_temp_receipt_no'), '', _bill_no, 'comlink', '', '', '', '', '', '', localtimestamp, _user_id, _user_no, _user_name, '', localtimestamp, _user_id, _user_no, _user_name, '');

			--select dlv_id into _bill_id from me_dlv where dlv_no=_bill_no;
			insert into public.wm_temp_receipt_b
			(temp_receipt_b_id, temp_receipt_id, part_no, part_name, part_spec, part_unit, part_qty_plan, part_qty_real, mo_no, temp_receipt_rmk01, temp_receipt_rmk02, temp_receipt_rmk03, temp_receipt_rmk04, temp_receipt_rmk05, temp_receipt_rmk06, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host)
			select af_auid(), _temp_receipt_id, b.part_no, c.part_name, c.part_spec, c.part_unit, a.finish_io_qty_ok, 0, a.mo_no, '', '', '', '', '', '', localtimestamp, _user_id, _user_no, _user_name, '', localtimestamp, _user_id, _user_no, _user_name, ''
			from me_finish_io_h a
			left join mo b on b.mo_no=a.mo_no
			left join pd_part c on c.part_no=b.part_no
			where me_finish_io_no=_bill_no;
		end if;

		select temp_receipt_id,temp_receipt_no into _temp_receipt_id,_temp_receipt_no from wm_temp_receipt where delivery_no=_bill_no;

		select part_qty_plan,part_qty_real into _part_qty_plan,_part_qty_real
		from wm_temp_receipt_b where temp_receipt_id=_temp_receipt_id and part_no=_part_no and mo_no=_mo_no;

		if _part_qty_plan < _part_qty_real+_part_qty then
			_err_msg := format('实际暂收数量(已暂收【%s】+现条码【%s】)大于计划(发货单)数量【%s】。', _part_qty_real::int,_part_qty::int,_part_qty_plan::int);
			res := row('false', _err_msg);
			return to_json(res);
		end if;

		if _part_qty_plan = _part_qty_real+_part_qty then
			update public.wm_temp_receipt_b set part_qty_real=part_qty_real+_part_qty,temp_receipt_rmk01='暂收完成',upd_time=localtimestamp
			where temp_receipt_id=_temp_receipt_id and part_no=_part_no and mo_no=_mo_no;
		else
			update public.wm_temp_receipt_b set part_qty_real=part_qty_real+_part_qty,upd_time=localtimestamp
			where temp_receipt_id=_temp_receipt_id and part_no=_part_no and mo_no=_mo_no;
		end if;

		insert into public.wm_temp_receipt_sn_part
		(temp_receipt_sn_id, temp_receipt_id, sn_no, part_no, part_name, part_spec, part_unit, part_qty, mo_no, temp_receipt_rmk01, temp_receipt_rmk02, temp_receipt_rmk03, temp_receipt_rmk04, temp_receipt_rmk05, temp_receipt_rmk06, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host)
		select af_auid(), _temp_receipt_id, _sn_no, _part_no, part_name, part_spec, part_unit, _part_qty, _mo_no, '', '', '', '', '', '', localtimestamp, _user_id, _user_no, _user_name, '', localtimestamp, _user_id, _user_no, _user_name, ''
		from pd_part
		where part_no=_part_no;

		---------------------------------------------------------------------------------------------------------------
		update public.wm_sn set sn_status='840',sn_status_name='暂收',invp_no='',invp_area_no='暂收库',
			upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
		where sn_no =_sn_no;

		update public.me_finish_io set me_finish_io_rmk4 ='暂收完成',upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
		where me_finish_io_no=_bill_no and sn_no=_sn_no;

		if not exists(select distinct 1 from me_finish_io where me_finish_io_no=_bill_no and coalesce(me_finish_io_rmk4,'')<>'暂收完成') then
			update public.me_finish_io_h set me_finish_io_rmk4 ='暂收完成',upd_time=localtimestamp
			where me_finish_io_no=_bill_no;
		end if;
		---------------------------------------------------------------------------------------------------------------
		if not exists(select distinct 1 from wm_temp_receipt_b where temp_receipt_id=_temp_receipt_id and temp_receipt_rmk01='') then
			update public.wm_temp_receipt set temp_receipt_rmk01='暂收完成'
			where temp_receipt_id=_temp_receipt_id;
		end if;

		if exists(select 1 from wm_temp_receipt where temp_receipt_id=_temp_receipt_id and coalesce(temp_receipt_rmk01,'')='暂收完成') then
			json_result := af_ax_wms_temp_receipt_push_qm_si_lot(json_build_object('user_no',_user_no,'host','PDA','selected',json_agg(json_build_object('temp_receipt_no',_temp_receipt_no,'temp_receipt_id',_temp_receipt_id)))::text);
			if json_result->>'successful'='false' then
				res := row('false', json_result->>'msg');
				return to_json(res);
			end if;
		end if;

		res := row('true', '仓库暂收完成');
		return to_json(res);

	EXCEPTION WHEN OTHERS THEN
		GET STACKED DIAGNOSTICS
			_err_msg_text = MESSAGE_TEXT,
			_err_pg_detail = PG_EXCEPTION_DETAIL;

		_err_msg := format('错误信息:%s,详情:%s',_err_msg_text,_err_msg_text);
		res := row('false',_err_msg);
		return to_json(res);
	END;
$function$
;

```


```sql
-- DROP FUNCTION public.af_ax_wms_temp_receipt_push_qm_si_lot(varchar);

CREATE OR REPLACE FUNCTION public.af_ax_wms_temp_receipt_push_qm_si_lot(datas character varying)
 RETURNS character varying
 LANGUAGE plpgsql
AS $function$
/*
 * 功能：  暂收单下推品质检验单
 * 描述：
 * 时间：
 * 开发者：
 */
	declare 
		json_datas json;
		_user_id text;
		_user_no text;
		_user_name text;
		_host text;

		_temp_receipt_no text;
		_temp_receipt_id text;
		_qm_si_lot_no text;
		_qm_si_lot_id text;
	
		_part_qty numeric;
		
		row_datas record;
		_client_no text;
		_client_name text;
	
		_err_msg_text text;
		_err_pg_detail text;
		_err_msg text;			
		res returntype;	
	
	begin
		json_datas := json(datas);
		_user_no := json_datas->>'user_no';
		_host := json_datas->>'host';
		
		json_datas := json(json_datas->'selected'->0);
		_temp_receipt_no := json_datas->>'temp_receipt_no';
		_temp_receipt_id := json_datas->>'temp_receipt_id';

		--insert into a_test_log values(datas, 'push_si_lot', localtimestamp);
	
		select user_id,user_name into _user_id,_user_name from ss_user where user_no=_user_no;
	
		if exists(select 1 from qm_si_lot_h where move_order_no=_temp_receipt_no) then
			res := row('false', 此暂收单已经下推检验单，不能二次下推);
			return to_json(res);
		end if;
	
		----------------------------------------------------------------------------------------------------------------
		
		for row_datas in (select temp_receipt_b_id,temp_receipt_id,part_no,mo_no from wm_temp_receipt_b where temp_receipt_id=_temp_receipt_id) loop
		
			_qm_si_lot_id := af_auid();

			select client_no,client_name into _client_no,_client_name from mo where mo_no=row_datas.mo_no;
		
			insert into public.qm_si_lot_h
			(si_lot_h_id, si_lot_h_no, si_lot_h_status, factory_no, factory_name, part_no, part_name, part_spec, part_idt, wkp_no, wkp_name, si_lot_qty, si_lot_move_type, si_lot_move_type_name, move_order_h_id, move_order_b_id, move_order_id, move_order_no, order_type, order_type_name, order_h_id, order_b_id, order_id, order_no, client_no, client_name, supplier_no, supplier_name, si_type, si_type_name, si_degree, si_degree_name, si_level, si_level_name, si_aql, si_lot_qty_ok, si_lot_qty_ng, si_conclusion_no, si_conclusion_name, si_is_pass, si_lot_h_rmk01, si_lot_h_rmk02, si_lot_h_rmk03, si_lot_h_rmk04, si_lot_h_rmk05, si_lot_h_rmk06, si_lot_h_rmk07, si_lot_h_rmk08, si_lot_h_rmk09, si_lot_h_rmk10, da_switch_id, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host, io_is_sucessed, io_times, io_last_time, delivery_order_no, si_sample_qty, si_lot_h_rmk11, si_lot_h_rmk12, si_lot_h_rmk13, si_lot_h_rmk14, si_lot_h_rmk15, si_lot_h_rmk16, si_lot_h_rmk17, si_lot_h_rmk18, si_lot_h_rmk19, si_lot_h_rmk20, si_lot_h_rmk21, si_lot_h_rmk22, si_lot_h_rmk23, si_lot_h_rmk24, si_lot_h_rmk25, si_lot_h_rmk26, si_lot_h_rmk27, si_lot_h_rmk28, si_lot_h_rmk29, si_lot_h_rmk30, si_lot_h_rmk31, price, priceafvat, lot_no, put_qty, si_lot_h_rmk32, whscode, canceled, qm_si_gist_no, ea_no, ea_name, si_lot_h_rmk33, si_lot_h_rmk34)
			select _qm_si_lot_id, af_ss_no_generate('wgjy_si_lot_h_no'), '5B8A5FD43EEB00004DFE', 'comlink', '深圳市金洋电子股份有限公司', part_no, part_name, coalesce(part_spec,''), '', '', '', part_qty_real, '315', '完工检验(复检)', _temp_receipt_id, '', _temp_receipt_id, _temp_receipt_no, 'MO', '生产订单', '', '', '', row_datas.mo_no, _client_no, _client_name, '', '', '20', '抽检', '20', '正常', '10', '一般', '', 0, 0, '', '', false, '', '', '', '', '', '', '', '', '', '', '', localtimestamp, _user_id, _user_no, _user_name, '', localtimestamp, _user_id, _user_no, _user_name, '', false, 0, null, '', 0, '', '', '', '', false, '', localtimestamp, null, '', '', '', '', false, '', '', '', '', '', '', 0, '', 0, 0, '', 0, '', '', '', '', '', '', '', ''
			from wm_temp_receipt_b
			where temp_receipt_b_id=row_datas.temp_receipt_b_id;

			insert into public.qm_si_lot_b_sn
			(si_lot_b_sn_id, si_lot_h_id, si_lot_b_id, sn_no, si_conclusion, qa_cause_no, qa_cause_name, si_lot_b_sn_rmk01, si_lot_b_sn_rmk02, si_lot_b_sn_rmk03, si_lot_b_sn_rmk04, si_lot_b_sn_rmk05, si_lot_b_sn_rmk06, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host, sn_pack_20, sn_pack_30, sn_pack_40, sn_pack_50, part_qty, mo_no, weight_gross, weight_net, pack_qty_used, ea_no, ea_name, sn_type, sn_type_name)
			select af_auid(), _qm_si_lot_id, '', a.sn_no, '', '', '', '', '', '', '', '', '', localtimestamp, _user_id, _user_no, _user_name, '', localtimestamp, _user_id, _user_no, _user_name, '', '20', '', '', '', a.part_qty, b.mo_no, b.weight_gross, b.weight_net, pack_qty_used, b.ea_no, b.ea_name, b.sn_type, b.sn_type_name
			from wm_temp_receipt_sn_part a
			left join wm_sn b on b.sn_no=a.sn_no
			where temp_receipt_id=row_datas.temp_receipt_id and a.part_no=row_datas.part_no and a.mo_no=row_datas.mo_no;
	
		end loop;
	

		update public.wm_temp_receipt set temp_receipt_rmk01='下推检验单完成',push_time=localtimestamp,upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
		where temp_receipt_no=_temp_receipt_no;
	
		res := row('true', '下推品质检验单完成');
		return to_json(res);
	
	EXCEPTION WHEN OTHERS THEN 
		GET STACKED DIAGNOSTICS 
			_err_msg_text = MESSAGE_TEXT,
			_err_pg_detail = PG_EXCEPTION_DETAIL;
	
		_err_msg := format('错误信息:%s,详情:%s',_err_msg_text,_err_msg_text);
		res := row('false',_err_msg);
		return to_json(res);			
	end;

$function$
;


```
