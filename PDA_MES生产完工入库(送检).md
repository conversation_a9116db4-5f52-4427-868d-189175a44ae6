```sql
-- DROP FUNCTION public.af_pda_ea_no_verify(varchar);

CREATE OR REPLACE FUNCTION public.af_pda_ea_no_verify(datas character varying)
 RETURNS character varying
 LANGUAGE plpgsql
AS $function$
/*
函数名称:
描述:校验产线编码是否有效：
 {"user_id":null,"token":"ad7a5afab10f7af4d4341f448dc9cb12","prog_id":null,"tx_user_no":"pda","user_no":"zsy","datas":[{"invp_no":"05.01","sn_no":""}]}
*/
declare 
   jsonDatas json;
   _res returntype;
   _sn_no text;
   _result json[];
   _ea_no  varchar; 
begin 
       ------------- 
	 insert into a_test_log
	 select datas,'af_pda_ea_no_verify',now();
	 ---------------------    
	  ------ 
    jsonDatas:=json(datas);  
    jsonDatas:=jsonDatas->'datas'->0; 
	 ----- 
    _ea_no:=jsonDatas->>'ea_no';    
   raise notice 'ea_no %',_ea_no;
  
  --校验产线是否存在
  if not exists(select 1 from ea where ea_no = _ea_no) then
  	_res := row('false',concat('扫码的产线编号：',_ea_no,' 不存在！！！'));
  	return to_json(_res);
  end if;
    ----- 
	SELECT ARRAY_AGG(ROW_TO_JSON(a))
	INTO _result
	FROM (select ea_no 
			from ea 
			where ea.ea_no =upper(_ea_no) 
			and ea.is_line =true
		) a;
   raise notice '_result %',_result; 
    ----- 
    return json_build_object ('successful',true,'msg','查询成功','datas',_result);
    --  return  json_object('{"successful","msg","datas"}',array['true','查询成功',_result]);
     raise notice '_result %',_result;
	 ---- 
	 ----- 
	_res:=row('true','提交成功！',_result);
	return to_json(_res);
END;
$function$
;


```


```sql
-- DROP FUNCTION public.af_jiaoyantiaoma_finish_io(varchar);

CREATE OR REPLACE FUNCTION public.af_jiaoyantiaoma_finish_io(datas character varying)
 RETURNS character varying
 LANGUAGE plpgsql
AS $function$  
/*
---扫描前校验   select af_sales_notice_bf('k')  af_jiaoyantiaoma 
*/		 

declare
    _datas json;
     _results returntype; 
    _sn_no varchar;  
    _re_datas varchar; 
    _mo_no text;
    _mo_no_f text;
    _ea_no text;
    _mo_parent_top text;
    _mo_ea_no text;
    _sn_status_name text;
    _sn_type text;
    _sn_type_name text;

	_part_no_sn text;
	_part_no_mo text;
   
   	_si_lot_h_id	text;
   	_si_lot_h_no	text;
   	_s_sn_no	text;
    tmp	json;
   
begin  
  		--- 
	 _datas:=json(datas); 
	-- _re_datas:=(_datas->'datas')::varchar; 
    _sn_no:=_datas->'datas'->0->>'sn_no';
   _ea_no:=_datas->'datas'->0->>'ea_no';
    raise notice '%',_sn_no;
   
  -- insert into a_test_log
  --	 select datas,'af_jiaoyantiaoma_finish_io',now();
		if (coalesce(_sn_no,'') = '') then 
    	_results:=row('false','外箱条码不能为空！');
   		return  to_json(_results);  
    end if; 
   
   	select ws.mo_no,part_no into _mo_no,_part_no_sn from wm_sn ws where ws.sn_no =_sn_no ;
    
    select m.workshop_no,m.mo_parent_top,part_no into _mo_ea_no,_mo_parent_top,_part_no_mo  from mo m where m.mo_no = _mo_no;
	if coalesce(_part_no_sn,'part_no_sn')<>coalesce(_part_no_mo,'part_no_mo') then
		_results := row('false','标签的物料编码与工单的物料编码不一致。');
		return to_json(_results);
	end if;
		
		if not exists(select 1 from wm_sn where sn_no=_sn_no)   then 
    	_results:=row('false','外箱条码:'||_sn_no||'不存在！');
   		return  to_json(_results);  
    end if; 
   
    if not exists(select 1 from wm_sn where sn_no=_sn_no and wm_sn.sn_type ='40'  )   then 
			select sn_type,sn_type_name from into _sn_type,_sn_type_name wm_sn where sn_no=_sn_no;
    	_results:=row('false','条码类型:['||_sn_type|| '-' || _sn_type_name ||']不是外箱类型！');
   		return  to_json(_results);  
    end if; 
   
	if not exists(select 1 from wm_sn where sn_no=_sn_no and sn_status='110') then
		select sn_status_name into _sn_status_name from wm_sn where sn_no=_sn_no;
		_results := row('false', '外箱条码状态:【'||_sn_status_name||'】不是确认状态(110),不允许完工送检');
		return to_json(_results);
	end if;
   --判断外箱条码是否已包装完成  pack_qty_used
   	if exists(select 1 from wm_sn where sn_no=_sn_no and coalesce (wm_sn.pack_qty_used,0) <> wm_sn.part_qty )   then 
    	_results:=row('false','外箱条码:'||_sn_no||'未包装完成，不允许完工送检！');
   		return  to_json(_results);  
    end if; 
   
    --判断是否江西产线2023-05-31 增加委外分厂产线校验
   	if exists(select 1 from ea where ea_no = _ea_no and ea_type3 like '%江西%')then
   		if not exists(select 1 from mo_rmb b
						left join mo_rmb_ea e on e.unode = b.unode 
						left join ea a on a.ea_id = e.ea_id 
						where b.mo_no = _mo_no and a.ea_no = _ea_no)then
			_results:=row('false','生产订单：'||_mo_no||'产线编号与入库产线编号不一致，不允许完工送检扫描！');
	   		return  to_json(_results);
   		end if;
   	else
		--条码工单对应的产线编号与参数产线是否一致验证；
	    if  _ea_no <> coalesce(_mo_ea_no,'_mo_ea_no') then 
	    	_results:=row('false','生产订单：'||_mo_no||'产线编号与入库产线编号不一致，不允许完工送检扫描！');
	   		return  to_json(_results);  
	    end if; 
   	end if;
   
    
   
   
   /* if not exists(select 1 from mo m where m.mo_no =_mo_no and m.mo_status in ('300','310') )   then 
    	_results:=row('false','生产订单：'||_mo_no||'不存在或状态已结案！');
   		return  to_json(_results);  
    end if; */
   	--判断条码是否存在送检记录
    if  exists(select 1 	from me_finish_io a ,me_finish_io_h h 
   				where a.me_finish_io_no = h.me_finish_io_no  
   				and a.sn_no = _sn_no and h.finish_io_status<>'230')   then 
   				--2023-05-17判断是否重工单
   				_mo_no_f := substring(_mo_no,1,1);
   				if(_mo_no_f = 'F')then
				   		--重工单校验：重工单+条码是否存在送检记录
				   		if exists(select 1 	from me_finish_io a ,me_finish_io_h h 
				   				where a.me_finish_io_no = h.me_finish_io_no  
				   				and a.sn_no = _sn_no and h.finish_io_status<>'230' and a.mo_no = _mo_no)then
				   				_results:=row('false','条码'||_sn_no||'已完工送检扫描，不能再次送检扫描！');
								return  to_json(_results);
				   		end if;
				 else
				        _results:=row('false','条码'||_sn_no||'已完工送检扫描，不能再次送检扫描！');
						return  to_json(_results);
				 end if;
		    	  
   	end if; 
   
   
   --判断条码是否在过程检验判定不合格2023-08-03
   if exists(select 1 from qm_si_lot_h h where h.order_no = _mo_no and h.si_lot_move_type = '303' and h.si_conclusion_name = '不合格')then
   		select si_lot_h_id ,si_lot_h_no into _si_lot_h_id,_si_lot_h_no from qm_si_lot_h h where h.order_no = _mo_no and h.si_lot_move_type = '303' and h.si_conclusion_name = '不合格';
   		if exists(select 1 from szjy_oqc_gc_disqualified d where d.si_lot_h_no = _si_lot_h_no and d.mo_no = _mo_no and d.disqualified_si_conclusion_name = '批退')then
   			--获取外箱绑定的条码
   			select array_to_json(array_agg(row_to_json(t))) into tmp
   			from(
   				select s.sn_no from wm_sn s where s.sn_pack_50  = _sn_no and s.sn_type = '00'
   			)t;
   			--循环外箱绑定的产品条码
   			for icount in 0..json_array_length(tmp)-1 loop
	   			_s_sn_no :=tmp->icount->>'sn_no';
   				if exists(select 1 from qm_si_lot_b_sn b where b.si_lot_h_id = _si_lot_h_id and b.sn_no = _s_sn_no)then
   					_results:=row('false','产品条码'||_s_sn_no||' 在OQC过程检验判定为不合格，不能进行完工入库送检，请确认！');
					return  to_json(_results);
   				end if;
   			end loop;
   			
   		end if;
   		
   end if;
   
   

   _results:=row('true','扫码成功','[{"sn_no":"'||_sn_no||'"}]');
	return  to_json(_results);  
	END;
$function$
;

```

```sql
 -- DROP FUNCTION public.af_me_finish_io_h_scan(varchar);

CREATE OR REPLACE FUNCTION public.af_me_finish_io_h_scan(datas character varying)
 RETURNS character varying
 LANGUAGE plpgsql
AS $function$  
 ---分三步　      　第一　插入　条码表　
 --         第二　 插入　出库表身 　 
 --         第三     插入　出库表头  
declare
    _datas json;
    results returntype;
   _user_no varchar;
   _scan json;
	_cr_dlv_h_id  varchar ;  -- 发货单 
    _cr_dlv_h_no  varchar; -- 通知单
   _time timestamp;  
    _today date;  
   _xsck  varchar; 
	  rb record; 
	 _tday text;   
	_ea_no text;
	_mo_no text;
   _user_id text;
   _user_name text;
  _me_finish_io_id_h text;
  _ea_name text;
  _me_finish_io_no text;
  _si_lot_h_id	text;
  _si_lot_h_no text;
 
_lot_no text;

_prod_cycle_cnt int;

  _mo_type text;
	_part_qty numeric;
 	row_datas record;	

begin
	_time:=now();  
	_cr_dlv_h_id:=af_auid();   
	_tday:=to_char(current_date,'yyyy-mm-dd');  	    
             
	_datas := json(datas);
	_scan:=_datas->'datas';
	_user_no:=_datas->>'user_no';
	 -- _mo_no:=_scan->0->>'mo_no';

	raise notice '_time  %, _id  %, _user_no  % ,  _cr_dlv_h_no  %',
                 _time,_cr_dlv_h_id,_user_no,_cr_dlv_h_no  ; 
      
     select su.user_id ,su.user_name into _user_id,_user_name from ss_user su where su.user_no =_user_no; 

	create temp table temp_table_scwgsj as (select a.ea_no,a.sn_no,b.mo_no,b.part_no,b.part_qty  from json_to_recordset(json(_scan))as a(sn_no text, ea_no text)
											left join wm_sn b on b.sn_no=a.sn_no);

	----------2025/04/07 增加不同周期产品分开提交的管控(仅支持华为产品)-------------------------------------------
	select count(prod_cycle) into _prod_cycle_cnt
	from (select distinct substring(split_part(sn_no,'/021748',2),1,4) as prod_cycle from temp_table_scwgsj) tt;
	if _prod_cycle_cnt>1  then
		results := row('false', format('提交生产入库的产品存在 %s 个周期，请分开提交.', _prod_cycle_cnt));
		return to_json(results);
	end if;
	----------------------------------------------------

	for row_datas in (select distinct ea_no, mo_no, part_no from temp_table_scwgsj) loop

		select sum(part_qty) into _part_qty from temp_table_scwgsj where mo_no=row_datas.mo_no;
		select e.ea_name into _ea_name  from ea e where e.ea_no = row_datas.ea_no ; 
		select case when coalesce(mo_type,'')='20' then '1' 
				when  coalesce(mo_type,'')='70' then '2'
				else '' end into _mo_type from mo where mo_no= row_datas.mo_no;
		

    	_me_finish_io_id_h:=af_auid();
    	_si_lot_h_id:=af_auid();
    	_me_finish_io_no:=af_ss_no_generate('finish_io_no'::character varying);
    	_si_lot_h_no:=af_ss_no_generate('wgjy_si_lot_h_no'::character varying);

		--_lot_no := concat('M',to_char(current_date,'YYMMDD'),row_datas.mo_no);
		_lot_no := 'MES888888';
		--写入库单表头    
    	insert into public.me_finish_io_h
			(me_finish_io_id_h, me_finish_io_no, finish_io_status, finish_io_datetime, factory_no, 
			mo_no, part_no, part_name, part_spec, part_unit, part_idt, lot_no, finish_io_qty_ok, 
			invp_no_ok, finish_io_qty_ng, invp_no_ng, finish_io_qty_scrap, invp_no_scrap, finish_io_qty_other, 
			invp_no_other, workshop_no, workshop_worker_no, workshop_worker_name, 
			me_finish_io_rmk1, me_finish_io_rmk2, me_finish_io_rmk3, me_finish_io_rmk4, 
			fb_id, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, 
			upd_time, upd_user, upd_user_no, upd_user_name, upd_host, 
			io_is_sucessed, io_times, io_last_time, mo_type)
		values ( 
			_me_finish_io_id_h, _me_finish_io_no, '200', now(), 'comlink', 
			row_datas.mo_no, row_datas.part_no, '', '', '', '', _lot_no, _part_qty, 
			'', 0, '', 0, '', 0, 
			'', row_datas.ea_no, _user_no, _user_name, 
			_ea_name, '20', '', '', 
			'', now(),_user_id,_user_no,_user_name,'pda',now(),_user_id,_user_no,_user_name,'pda',
			false, 0, null, _mo_type);

     	---写入表明细
     	insert into public.me_finish_io
			(me_finish_io_id, me_finish_io_no, finish_io_status, finish_io_datetime, factory_no, mo_no, part_no, part_name, part_spec, 
			part_unit, part_idt, lot_no, finish_io_qty_ok, invp_no_ok, finish_io_qty_ng, invp_no_ng, finish_io_qty_scrap, invp_no_scrap, 
			finish_io_qty_other, invp_no_other, me_finish_io_rmk1, me_finish_io_rmk2, me_finish_io_rmk3, me_finish_io_rmk4, fb_id, 
			crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host, 
			io_is_sucessed, io_times, io_last_time, sn_no)		
		select af_auid(), _me_finish_io_no, '210', now(),'comlink',ws.mo_no ,ws.part_no ,ws.part_name ,pp.part_spec ,
			pp.part_unit ,'',_lot_no,ws.part_qty ,'',0,'',0,'',
			0,'','','','','','',now(),_user_id,_user_no,_user_name,'pda', now(),_user_id,_user_no,_user_name,'pda',
		false, 0, null, ws.sn_no 
		from temp_table_scwgsj a
		left join wm_sn ws on ws.sn_no = a.sn_no 
		left join pd_part pp on pp.part_no = ws.part_no 
		left join av_ss_qty_unit asqu on asqu.qty_unit_no = pp.part_unit
		where a.mo_no=row_datas.mo_no;
	

 		insert into qm_si_lot_h(
			si_lot_h_id,si_lot_h_no,si_lot_h_status,factory_no,factory_name,part_no,part_name,
			part_spec,part_idt,wkp_no,wkp_name,si_lot_qty,si_lot_move_type,si_lot_move_type_name,
			move_order_h_id,move_order_b_id,move_order_id,move_order_no,order_type,order_type_name,
			order_h_id,order_b_id,order_id,order_no,client_no,client_name,supplier_no,supplier_name,
			si_type,si_type_name,si_degree,si_degree_name,si_level,si_level_name,si_aql,si_lot_qty_ok,
			si_lot_qty_ng,si_conclusion_no,si_conclusion_name,si_is_pass,si_lot_h_rmk01,si_lot_h_rmk02,
			si_lot_h_rmk03,si_lot_h_rmk04,si_lot_h_rmk05,si_lot_h_rmk06,si_lot_h_rmk07,si_lot_h_rmk08,
			si_lot_h_rmk09,si_lot_h_rmk10,si_lot_h_rmk11,si_lot_h_rmk12,si_lot_h_rmk13,si_lot_h_rmk14,
			da_switch_id,ea_no,ea_name,
			crt_time,crt_user,crt_user_no,crt_user_name,crt_host,
			upd_time,upd_user,upd_user_no,upd_user_name,upd_host,
			io_is_sucessed,io_times,io_last_time)
		select 
			_si_lot_h_id,_si_lot_h_no,'5B8A5FD43EEB00004DFE',m.factory_no,sf.factory_name,m.part_no,p.part_name,
			p.part_spec,null,'','',_part_qty,'310','完工检验',null,null,
			_me_finish_io_id_h,_me_finish_io_no,'MO','生产订单',_me_finish_io_id_h,null,null,m.mo_no ,m.client_no,
			m.client_name,null,null,'20','抽检','20','正常','10','一般','',0,0,
			null,null,false,null,null,
			null,null,null,null,null,null,
			null,null,null,null,null,null,
			null,row_datas.ea_no,_ea_name,
			now(),_user_id,_user_no,_user_name,'pda',
			now(),_user_id,_user_no,_user_name,'pda',
			false,0,null
		from (select * from mo where mo_no=row_datas.mo_no) m
		left join ss_factory sf on sf.factory_no = m.factory_no
		left join pd_part p on p.part_no=m.part_no;


		--写入送检单条码明细
		INSERT INTO public.qm_si_lot_b_sn
			(si_lot_b_sn_id, si_lot_h_id, si_lot_b_id, sn_no, si_conclusion, qa_cause_no, qa_cause_name, 
			si_lot_b_sn_rmk01, si_lot_b_sn_rmk02, si_lot_b_sn_rmk03, si_lot_b_sn_rmk04, si_lot_b_sn_rmk05, 
			si_lot_b_sn_rmk06, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, 
			upd_time, upd_user, upd_user_no, upd_user_name, upd_host, 
			sn_pack_20, sn_pack_30, sn_pack_40, sn_pack_50, part_qty, 
			mo_no, weight_gross, weight_net, pack_qty_used, ea_no, ea_name,
			sn_type,sn_type_name)
		select 
			af_auid(), _si_lot_h_id,af_auid(),ws.sn_no ,null,null,null,
			null,null,null,null,null,
			null,now(),_user_id,_user_no,_user_name,'pda',
			now(),_user_id,_user_no,_user_name,'pda',
			ws.sn_pack_20 ,ws.sn_pack_30,ws.sn_pack_40,ws.sn_pack_50,ws.part_qty ,
			ws.mo_no ,ws.weight_gross ,ws.weight_net ,ws.pack_qty_used ,ws.ea_no ,ws.ea_name ,
			ws.sn_type,ws.sn_type_name
		from temp_table_scwgsj a
		left join wm_sn ws on a.sn_no = ws.sn_no
		where a.mo_no=row_datas.mo_no 	
		union all 
		select 
			af_auid(), _si_lot_h_id,af_auid(),ws.sn_no ,null,null,null,
			null,null,null,null,null,
			null,now(),_user_id,_user_no,_user_name,'pda',
			now(),_user_id,_user_no,_user_name,'pda',
			ws.sn_pack_20 ,ws.sn_pack_30,ws.sn_pack_40,ws.sn_pack_50,ws.part_qty ,
			ws.mo_no ,ws.weight_gross ,ws.weight_net ,ws.pack_qty_used ,ws.ea_no ,ws.ea_name ,
			ws.sn_type,ws.sn_type_name
		from temp_table_scwgsj a
		join wm_sn ws on a.sn_no = ws.sn_pack_50
		where a.mo_no=row_datas.mo_no;

	end loop;

	results := row('true','成功!');      
    return to_json(results);   
end;  
 $function$
;
```
// ... existing code ...
 $function$
 ;

---
### **完整流程分析 (V2 - 包含校验、批量提交)**

基于您更新的文件，现在 `PDA_MES生产完工入库(送检).md` 构成了该业务的完整闭环。以下是覆盖全部三个函数的综合分析。

### **整体业务流程概述**

这个业务流程描绘了PDA（手持数据终端）操作员在生产线完成产品包装后，如何将产品批量送交质量部门检验的全过程。它由三个函数串联而成：

1.  **第一步：验证产线 (`af_pda_ea_no_verify`)**
    *   操作员首先扫描所在产线的条码。
    *   系统调用此函数，验证产线编码是否真实、有效，为后续操作提供一个合法的“地点”上下文。

2.  **第二步：逐个扫描并校验外箱 (`af_jiaoyantiaoma_finish_io`)**
    *   操作员开始逐一扫描本次希望送检的所有产品的外箱条码。
    *   每扫描一个条码，系统就调用此函数进行 **实时前置校验**。此函数作为“守门员”，会检查条码状态、物料一致性、是否重复送检、是否有OQC不合格等一系列规则。只有通过校验的条码才会被加入到待提交列表。

3.  **第三步：批量提交生成单据 (`af_me_finish_io_h_scan`)**
    *   操作员扫描完所有外箱后，点击“提交”按钮。
    *   系统将待提交列表中的所有有效条码，一次性传递给此函数。
    *   此函数是核心的数据处理后台，它会 **批量创建**“生产完工入库单”和“完工检验单”，并将所有相关的条码明细关联到这些新生成的单据上，正式完成送检流程。

---

### **函数详细分析**

#### **1. `af_pda_ea_no_verify` (产线校验函数)**

*   **功能概述**: 这是一个简单的验证服务，用于确认PDA扫描的产线编码（`ea_no`）是否在 `ea` 表中存在且被标记为有效产线 (`is_line = true`)。
*   **业务逻辑**: 保证后续的所有操作都发生在一个已知的、正确的生产单元上，防止操作员选错产线。
*   **数据逻辑**:
    *   输入: `{ "datas": [{ "ea_no": "产线编码" }] }`
    *   处理: `SELECT` from `ea` table.
    *   输出: 成功则返回产线信息，失败则返回错误提示。
*   **代码逻辑**: 使用 `IF NOT EXISTS` 进行判断，逻辑清晰直接。

---

#### **2. `af_jiaoyantiaoma_finish_io` (送检前置校验函数)**

*   **功能概述**: 流程的“守门员”，对每一个扫描的外箱条码进行严格的实时验证，确保其符合送检条件。
*   **核心业务校验点**:
    *   **条码有效性**: 存在、类型为外箱、状态为“确认”。
    *   **数据一致性**: 条码的物料/工单信息与主数据一致。
    *   **流程完整性**: 包装已满箱、未重复送检（重工单除外）。
    *   **质量合规性**: 未在OQC过程检验中被判定为“批退”。
*   **数据逻辑**: 广泛关联 `wm_sn`, `mo`, `me_finish_io`, `qm_si_lot_h` 等多个核心表进行交叉验证。
*   **代码逻辑**: 采用串行 `IF...THEN` 结构，一旦有任何校验失败则立即返回错误并中断，效率高，反馈明确。

---

#### **3. `af_me_finish_io_h_scan` (批量完工送检处理函数)**

*   **功能概述**: 这是流程的最终执行者。它接收所有已通过前置校验的条码列表，然后批量生成“生产完工单”和“质量检验单”。
*   **业务逻辑分析**:
    1.  **批量处理**: 首先将传入的条码列表存入一个临时表 `temp_table_scwgsj`。这是处理批量数据的最佳实践，极大提升了性能。
    2.  **周期管控 (华为产品)**: 在处理前，会检查所有待提交的条码是否属于同一个生产周期（通过解析 `sn_no` 字符串判断）。如果来自多个周期，则会报错，要求用户分开提交。这是一个非常具体的客户化业务规则。
    3.  **分组生成单据**: 核心逻辑是 **按（产线、工单、物料）对条码进行分组**。它会 `LOOP` 遍历每一个不同的组，为每一个组都创建一套独立的“生产完工单” (`me_finish_io_h`) 和“质量检验单” (`qm_si_lot_h`)。
    4.  **关联内外码**: 在生成检验单明细 (`qm_si_lot_b_sn`) 时，使用 `UNION ALL` 将外箱本身及其包含的所有内包装/产品码都添加到检验清单中，确保检验的全面性。
    5.  **批次号**: `_lot_no` 被固定设置为 `'MES888888'`，这可能是一个特定的业务需求，表示这是一个由MES系统生成的内部流转批次。
*   **数据逻辑分析**:
    *   **输入**: `{ "user_no": "...", "datas": [{ "sn_no": "...", "ea_no": "..." }, ...] }`
    *   **核心表**:
        *   **写入**: `me_finish_io_h`, `me_finish_io`, `qm_si_lot_h`, `qm_si_lot_b_sn`。
        *   **读取**: `wm_sn`, `mo`, `pd_part`, `ea`, `ss_user`。
    *   **数据流**: `输入JSON -> 临时表 -> 按组循环 -> 批量INSERT`。
*   **代码逻辑分析**:
    *   使用 `json_to_recordset` 将 JSON 数组高效地转换为行记录。
    *   `CREATE TEMP TABLE` 的使用是代码的一个亮点。
    *   `FOR LOOP` 循环处理分组逻辑。
    *   `INSERT INTO ... SELECT ...` 的模式被用来从关联表中高效地批量插入数据。

---

### **核心表关联逻辑 (完整流程)**

```mermaid
graph TD
    subgraph "输入与校验 (af_jiaoyantiaoma_finish_io)"
        EA_IN[("ea 产线")]
        SN_IN[("wm_sn 外箱")]
        MO_IN[("mo 工单")]
        MFI_IN[("me_finish_io 完工单")]
        QSLH_IN[("qm_si_lot_h 检验批")]
    end
    
    subgraph "批量创建 (af_me_finish_io_h_scan)"
        MFH_OUT[me_finish_io_h 完工单头]
        MFI_OUT[me_finish_io 完工单明细]
        QSLH_OUT[qm_si_lot_h 检验批头]
        QSLBSN_OUT[qm_si_lot_b_sn 检验批明细]
    end
    
    subgraph "核心主数据"
        SN[wm_sn]
        MO[mo]
        PD[pd_part]
    end

    %% Flow
    SN_IN -- 校验 --> MO_IN & MFI_IN & QSLH_IN
    
    SN_IN -- "通过校验后" --> MFH_OUT
    
    %% Creation Logic
    MFH_OUT -- "创建" --> MFI_OUT
    MFH_OUT -- "下推" --> QSLH_OUT
    QSLH_OUT -- "创建" --> QSLBSN_OUT

    %% Master Data Links
    MO -- "关联" --> MFH_OUT
    PD -- "关联" --> MFH_OUT
    PD -- "关联" --> MFI_OUT
    PD -- "关联" --> QSLH_OUT

    SN -- "关联" --> MFI_OUT
    SN -- "关联" --> QSLBSN_OUT
    
    %% Styling
    style MFH_OUT fill:#99ccff
    style QSLH_OUT fill:#99ccff
    style MFI_OUT fill:#c3e6ff
    style QSLBSN_OUT fill:#c3e6ff
    
```
