```sql
-- DROP FUNCTION public.af_pda_ea_no_verify(varchar);

CREATE OR REPLACE FUNCTION public.af_pda_ea_no_verify(datas character varying)
 RETURNS character varying
 LANGUAGE plpgsql
AS $function$
/*
函数名称:
描述:校验产线编码是否有效：
 {"user_id":null,"token":"ad7a5afab10f7af4d4341f448dc9cb12","prog_id":null,"tx_user_no":"pda","user_no":"zsy","datas":[{"invp_no":"05.01","sn_no":""}]}
*/
declare 
   jsonDatas json;
   _res returntype;
   _sn_no text;
   _result json[];
   _ea_no  varchar; 
begin 
       ------------- 
	 insert into a_test_log
	 select datas,'af_pda_ea_no_verify',now();
	 ---------------------    
	  ------ 
    jsonDatas:=json(datas);  
    jsonDatas:=jsonDatas->'datas'->0; 
	 ----- 
    _ea_no:=jsonDatas->>'ea_no';    
   raise notice 'ea_no %',_ea_no;
  
  --校验产线是否存在
  if not exists(select 1 from ea where ea_no = _ea_no) then
  	_res := row('false',concat('扫码的产线编号：',_ea_no,' 不存在！！！'));
  	return to_json(_res);
  end if;
    ----- 
	SELECT ARRAY_AGG(ROW_TO_JSON(a))
	INTO _result
	FROM (select ea_no 
			from ea 
			where ea.ea_no =upper(_ea_no) 
			and ea.is_line =true
		) a;
   raise notice '_result %',_result; 
    ----- 
    return json_build_object ('successful',true,'msg','查询成功','datas',_result);
    --  return  json_object('{"successful","msg","datas"}',array['true','查询成功',_result]);
     raise notice '_result %',_result;
	 ---- 
	 ----- 
	_res:=row('true','提交成功！',_result);
	return to_json(_res);
END;
$function$
;


```


```sql
-- DROP FUNCTION public.af_jiaoyantiaoma_finish_io(varchar);

CREATE OR REPLACE FUNCTION public.af_jiaoyantiaoma_finish_io(datas character varying)
 RETURNS character varying
 LANGUAGE plpgsql
AS $function$  
/*
---扫描前校验   select af_sales_notice_bf('k')  af_jiaoyantiaoma 
*/		 

declare
    _datas json;
     _results returntype; 
    _sn_no varchar;  
    _re_datas varchar; 
    _mo_no text;
    _mo_no_f text;
    _ea_no text;
    _mo_parent_top text;
    _mo_ea_no text;
    _sn_status_name text;
    _sn_type text;
    _sn_type_name text;

	_part_no_sn text;
	_part_no_mo text;
   
   	_si_lot_h_id	text;
   	_si_lot_h_no	text;
   	_s_sn_no	text;
    tmp	json;
   
begin  
  		--- 
	 _datas:=json(datas); 
	-- _re_datas:=(_datas->'datas')::varchar; 
    _sn_no:=_datas->'datas'->0->>'sn_no';
   _ea_no:=_datas->'datas'->0->>'ea_no';
    raise notice '%',_sn_no;
   
  -- insert into a_test_log
  --	 select datas,'af_jiaoyantiaoma_finish_io',now();
		if (coalesce(_sn_no,'') = '') then 
    	_results:=row('false','外箱条码不能为空！');
   		return  to_json(_results);  
    end if; 
   
   	select ws.mo_no,part_no into _mo_no,_part_no_sn from wm_sn ws where ws.sn_no =_sn_no ;
    
    select m.workshop_no,m.mo_parent_top,part_no into _mo_ea_no,_mo_parent_top,_part_no_mo  from mo m where m.mo_no = _mo_no;
	if coalesce(_part_no_sn,'part_no_sn')<>coalesce(_part_no_mo,'part_no_mo') then
		_results := row('false','标签的物料编码与工单的物料编码不一致。');
		return to_json(_results);
	end if;
		
		if not exists(select 1 from wm_sn where sn_no=_sn_no)   then 
    	_results:=row('false','外箱条码:'||_sn_no||'不存在！');
   		return  to_json(_results);  
    end if; 
   
    if not exists(select 1 from wm_sn where sn_no=_sn_no and wm_sn.sn_type ='40'  )   then 
			select sn_type,sn_type_name from into _sn_type,_sn_type_name wm_sn where sn_no=_sn_no;
    	_results:=row('false','条码类型:['||_sn_type|| '-' || _sn_type_name ||']不是外箱类型！');
   		return  to_json(_results);  
    end if; 
   
	if not exists(select 1 from wm_sn where sn_no=_sn_no and sn_status='110') then
		select sn_status_name into _sn_status_name from wm_sn where sn_no=_sn_no;
		_results := row('false', '外箱条码状态:【'||_sn_status_name||'】不是确认状态(110),不允许完工送检');
		return to_json(_results);
	end if;
   --判断外箱条码是否已包装完成  pack_qty_used
   	if exists(select 1 from wm_sn where sn_no=_sn_no and coalesce (wm_sn.pack_qty_used,0) <> wm_sn.part_qty )   then 
    	_results:=row('false','外箱条码:'||_sn_no||'未包装完成，不允许完工送检！');
   		return  to_json(_results);  
    end if; 
   
    --判断是否江西产线2023-05-31 增加委外分厂产线校验
   	if exists(select 1 from ea where ea_no = _ea_no and ea_type3 like '%江西%')then
   		if not exists(select 1 from mo_rmb b
						left join mo_rmb_ea e on e.unode = b.unode 
						left join ea a on a.ea_id = e.ea_id 
						where b.mo_no = _mo_no and a.ea_no = _ea_no)then
			_results:=row('false','生产订单：'||_mo_no||'产线编号与入库产线编号不一致，不允许完工送检扫描！');
	   		return  to_json(_results);
   		end if;
   	else
		--条码工单对应的产线编号与参数产线是否一致验证；
	    if  _ea_no <> coalesce(_mo_ea_no,'_mo_ea_no') then 
	    	_results:=row('false','生产订单：'||_mo_no||'产线编号与入库产线编号不一致，不允许完工送检扫描！');
	   		return  to_json(_results);  
	    end if; 
   	end if;
   
    
   
   
   /* if not exists(select 1 from mo m where m.mo_no =_mo_no and m.mo_status in ('300','310') )   then 
    	_results:=row('false','生产订单：'||_mo_no||'不存在或状态已结案！');
   		return  to_json(_results);  
    end if; */
   	--判断条码是否存在送检记录
    if  exists(select 1 	from me_finish_io a ,me_finish_io_h h 
   				where a.me_finish_io_no = h.me_finish_io_no  
   				and a.sn_no = _sn_no and h.finish_io_status<>'230')   then 
   				--2023-05-17判断是否重工单
   				_mo_no_f := substring(_mo_no,1,1);
   				if(_mo_no_f = 'F')then
				   		--重工单校验：重工单+条码是否存在送检记录
				   		if exists(select 1 	from me_finish_io a ,me_finish_io_h h 
				   				where a.me_finish_io_no = h.me_finish_io_no  
				   				and a.sn_no = _sn_no and h.finish_io_status<>'230' and a.mo_no = _mo_no)then
				   				_results:=row('false','条码'||_sn_no||'已完工送检扫描，不能再次送检扫描！');
								return  to_json(_results);
				   		end if;
				 else
				        _results:=row('false','条码'||_sn_no||'已完工送检扫描，不能再次送检扫描！');
						return  to_json(_results);
				 end if;
		    	  
   	end if; 
   
   
   --判断条码是否在过程检验判定不合格2023-08-03
   if exists(select 1 from qm_si_lot_h h where h.order_no = _mo_no and h.si_lot_move_type = '303' and h.si_conclusion_name = '不合格')then
   		select si_lot_h_id ,si_lot_h_no into _si_lot_h_id,_si_lot_h_no from qm_si_lot_h h where h.order_no = _mo_no and h.si_lot_move_type = '303' and h.si_conclusion_name = '不合格';
   		if exists(select 1 from szjy_oqc_gc_disqualified d where d.si_lot_h_no = _si_lot_h_no and d.mo_no = _mo_no and d.disqualified_si_conclusion_name = '批退')then
   			--获取外箱绑定的条码
   			select array_to_json(array_agg(row_to_json(t))) into tmp
   			from(
   				select s.sn_no from wm_sn s where s.sn_pack_50  = _sn_no and s.sn_type = '00'
   			)t;
   			--循环外箱绑定的产品条码
   			for icount in 0..json_array_length(tmp)-1 loop
	   			_s_sn_no :=tmp->icount->>'sn_no';
   				if exists(select 1 from qm_si_lot_b_sn b where b.si_lot_h_id = _si_lot_h_id and b.sn_no = _s_sn_no)then
   					_results:=row('false','产品条码'||_s_sn_no||' 在OQC过程检验判定为不合格，不能进行完工入库送检，请确认！');
					return  to_json(_results);
   				end if;
   			end loop;
   			
   		end if;
   		
   end if;
   
   

   _results:=row('true','扫码成功','[{"sn_no":"'||_sn_no||'"}]');
	return  to_json(_results);  
	END;
$function$
;

```

```sql
 -- DROP FUNCTION public.af_me_finish_io_h_scan(varchar);

CREATE OR REPLACE FUNCTION public.af_me_finish_io_h_scan(datas character varying)
 RETURNS character varying
 LANGUAGE plpgsql
AS $function$  
 ---分三步　      　第一　插入　条码表　
 --         第二　 插入　出库表身 　 
 --         第三     插入　出库表头  
declare
    _datas json;
    results returntype;
   _user_no varchar;
   _scan json;
	_cr_dlv_h_id  varchar ;  -- 发货单 
    _cr_dlv_h_no  varchar; -- 通知单
   _time timestamp;  
    _today date;  
   _xsck  varchar; 
	  rb record; 
	 _tday text;   
	_ea_no text;
	_mo_no text;
   _user_id text;
   _user_name text;
  _me_finish_io_id_h text;
  _ea_name text;
  _me_finish_io_no text;
  _si_lot_h_id	text;
  _si_lot_h_no text;
 
_lot_no text;

_prod_cycle_cnt int;

  _mo_type text;
	_part_qty numeric;
 	row_datas record;	

begin
	_time:=now();  
	_cr_dlv_h_id:=af_auid();   
	_tday:=to_char(current_date,'yyyy-mm-dd');  	    
             
	_datas := json(datas);
	_scan:=_datas->'datas';
	_user_no:=_datas->>'user_no';
	 -- _mo_no:=_scan->0->>'mo_no';

	raise notice '_time  %, _id  %, _user_no  % ,  _cr_dlv_h_no  %',
                 _time,_cr_dlv_h_id,_user_no,_cr_dlv_h_no  ; 
      
     select su.user_id ,su.user_name into _user_id,_user_name from ss_user su where su.user_no =_user_no; 

	create temp table temp_table_scwgsj as (select a.ea_no,a.sn_no,b.mo_no,b.part_no,b.part_qty  from json_to_recordset(json(_scan))as a(sn_no text, ea_no text)
											left join wm_sn b on b.sn_no=a.sn_no);

	----------2025/04/07 增加不同周期产品分开提交的管控(仅支持华为产品)-------------------------------------------
	select count(prod_cycle) into _prod_cycle_cnt
	from (select distinct substring(split_part(sn_no,'/021748',2),1,4) as prod_cycle from temp_table_scwgsj) tt;
	if _prod_cycle_cnt>1  then
		results := row('false', format('提交生产入库的产品存在 %s 个周期，请分开提交.', _prod_cycle_cnt));
		return to_json(results);
	end if;
	----------------------------------------------------

	for row_datas in (select distinct ea_no, mo_no, part_no from temp_table_scwgsj) loop

		select sum(part_qty) into _part_qty from temp_table_scwgsj where mo_no=row_datas.mo_no;
		select e.ea_name into _ea_name  from ea e where e.ea_no = row_datas.ea_no ; 
		select case when coalesce(mo_type,'')='20' then '1' 
				when  coalesce(mo_type,'')='70' then '2'
				else '' end into _mo_type from mo where mo_no= row_datas.mo_no;
		

    	_me_finish_io_id_h:=af_auid();
    	_si_lot_h_id:=af_auid();
    	_me_finish_io_no:=af_ss_no_generate('finish_io_no'::character varying);
    	_si_lot_h_no:=af_ss_no_generate('wgjy_si_lot_h_no'::character varying);

		--_lot_no := concat('M',to_char(current_date,'YYMMDD'),row_datas.mo_no);
		_lot_no := 'MES888888';
		--写入库单表头    
    	insert into public.me_finish_io_h
			(me_finish_io_id_h, me_finish_io_no, finish_io_status, finish_io_datetime, factory_no, 
			mo_no, part_no, part_name, part_spec, part_unit, part_idt, lot_no, finish_io_qty_ok, 
			invp_no_ok, finish_io_qty_ng, invp_no_ng, finish_io_qty_scrap, invp_no_scrap, finish_io_qty_other, 
			invp_no_other, workshop_no, workshop_worker_no, workshop_worker_name, 
			me_finish_io_rmk1, me_finish_io_rmk2, me_finish_io_rmk3, me_finish_io_rmk4, 
			fb_id, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, 
			upd_time, upd_user, upd_user_no, upd_user_name, upd_host, 
			io_is_sucessed, io_times, io_last_time, mo_type)
		values ( 
			_me_finish_io_id_h, _me_finish_io_no, '200', now(), 'comlink', 
			row_datas.mo_no, row_datas.part_no, '', '', '', '', _lot_no, _part_qty, 
			'', 0, '', 0, '', 0, 
			'', row_datas.ea_no, _user_no, _user_name, 
			_ea_name, '20', '', '', 
			'', now(),_user_id,_user_no,_user_name,'pda',now(),_user_id,_user_no,_user_name,'pda',
			false, 0, null, _mo_type);

     	---写入表明细
     	insert into public.me_finish_io
			(me_finish_io_id, me_finish_io_no, finish_io_status, finish_io_datetime, factory_no, mo_no, part_no, part_name, part_spec, 
			part_unit, part_idt, lot_no, finish_io_qty_ok, invp_no_ok, finish_io_qty_ng, invp_no_ng, finish_io_qty_scrap, invp_no_scrap, 
			finish_io_qty_other, invp_no_other, me_finish_io_rmk1, me_finish_io_rmk2, me_finish_io_rmk3, me_finish_io_rmk4, fb_id, 
			crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host, 
			io_is_sucessed, io_times, io_last_time, sn_no)		
		select af_auid(), _me_finish_io_no, '210', now(),'comlink',ws.mo_no ,ws.part_no ,ws.part_name ,pp.part_spec ,
			pp.part_unit ,'',_lot_no,ws.part_qty ,'',0,'',0,'',
			0,'','','','','','',now(),_user_id,_user_no,_user_name,'pda', now(),_user_id,_user_no,_user_name,'pda',
		false, 0, null, ws.sn_no 
		from temp_table_scwgsj a
		left join wm_sn ws on ws.sn_no = a.sn_no 
		left join pd_part pp on pp.part_no = ws.part_no 
		left join av_ss_qty_unit asqu on asqu.qty_unit_no = pp.part_unit
		where a.mo_no=row_datas.mo_no;
	

 		insert into qm_si_lot_h(
			si_lot_h_id,si_lot_h_no,si_lot_h_status,factory_no,factory_name,part_no,part_name,
			part_spec,part_idt,wkp_no,wkp_name,si_lot_qty,si_lot_move_type,si_lot_move_type_name,
			move_order_h_id,move_order_b_id,move_order_id,move_order_no,order_type,order_type_name,
			order_h_id,order_b_id,order_id,order_no,client_no,client_name,supplier_no,supplier_name,
			si_type,si_type_name,si_degree,si_degree_name,si_level,si_level_name,si_aql,si_lot_qty_ok,
			si_lot_qty_ng,si_conclusion_no,si_conclusion_name,si_is_pass,si_lot_h_rmk01,si_lot_h_rmk02,
			si_lot_h_rmk03,si_lot_h_rmk04,si_lot_h_rmk05,si_lot_h_rmk06,si_lot_h_rmk07,si_lot_h_rmk08,
			si_lot_h_rmk09,si_lot_h_rmk10,si_lot_h_rmk11,si_lot_h_rmk12,si_lot_h_rmk13,si_lot_h_rmk14,
			da_switch_id,ea_no,ea_name,
			crt_time,crt_user,crt_user_no,crt_user_name,crt_host,
			upd_time,upd_user,upd_user_no,upd_user_name,upd_host,
			io_is_sucessed,io_times,io_last_time)
		select 
			_si_lot_h_id,_si_lot_h_no,'5B8A5FD43EEB00004DFE',m.factory_no,sf.factory_name,m.part_no,p.part_name,
			p.part_spec,null,'','',_part_qty,'310','完工检验',null,null,
			_me_finish_io_id_h,_me_finish_io_no,'MO','生产订单',_me_finish_io_id_h,null,null,m.mo_no ,m.client_no,
			m.client_name,null,null,'20','抽检','20','正常','10','一般','',0,0,
			null,null,false,null,null,
			null,null,null,null,null,null,
			null,null,null,null,null,null,
			null,row_datas.ea_no,_ea_name,
			now(),_user_id,_user_no,_user_name,'pda',
			now(),_user_id,_user_no,_user_name,'pda',
			false,0,null
		from (select * from mo where mo_no=row_datas.mo_no) m
		left join ss_factory sf on sf.factory_no = m.factory_no
		left join pd_part p on p.part_no=m.part_no;


		--写入送检单条码明细
		INSERT INTO public.qm_si_lot_b_sn
			(si_lot_b_sn_id, si_lot_h_id, si_lot_b_id, sn_no, si_conclusion, qa_cause_no, qa_cause_name, 
			si_lot_b_sn_rmk01, si_lot_b_sn_rmk02, si_lot_b_sn_rmk03, si_lot_b_sn_rmk04, si_lot_b_sn_rmk05, 
			si_lot_b_sn_rmk06, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, 
			upd_time, upd_user, upd_user_no, upd_user_name, upd_host, 
			sn_pack_20, sn_pack_30, sn_pack_40, sn_pack_50, part_qty, 
			mo_no, weight_gross, weight_net, pack_qty_used, ea_no, ea_name,
			sn_type,sn_type_name)
		select 
			af_auid(), _si_lot_h_id,af_auid(),ws.sn_no ,null,null,null,
			null,null,null,null,null,
			null,now(),_user_id,_user_no,_user_name,'pda',
			now(),_user_id,_user_no,_user_name,'pda',
			ws.sn_pack_20 ,ws.sn_pack_30,ws.sn_pack_40,ws.sn_pack_50,ws.part_qty ,
			ws.mo_no ,ws.weight_gross ,ws.weight_net ,ws.pack_qty_used ,ws.ea_no ,ws.ea_name ,
			ws.sn_type,ws.sn_type_name
		from temp_table_scwgsj a
		left join wm_sn ws on a.sn_no = ws.sn_no
		where a.mo_no=row_datas.mo_no 	
		union all 
		select 
			af_auid(), _si_lot_h_id,af_auid(),ws.sn_no ,null,null,null,
			null,null,null,null,null,
			null,now(),_user_id,_user_no,_user_name,'pda',
			now(),_user_id,_user_no,_user_name,'pda',
			ws.sn_pack_20 ,ws.sn_pack_30,ws.sn_pack_40,ws.sn_pack_50,ws.part_qty ,
			ws.mo_no ,ws.weight_gross ,ws.weight_net ,ws.pack_qty_used ,ws.ea_no ,ws.ea_name ,
			ws.sn_type,ws.sn_type_name
		from temp_table_scwgsj a
		join wm_sn ws on a.sn_no = ws.sn_pack_50
		where a.mo_no=row_datas.mo_no;

	end loop;

	results := row('true','成功!');      
    return to_json(results);   
end;  
 $function$
;
```
// ... existing code ...
 $function$
 ;

---
### **完整流程分析 (V2 - 包含校验、批量提交)**

基于您更新的文件，现在 `PDA_MES生产完工入库(送检).md` 构成了该业务的完整闭环。以下是覆盖全部三个函数的综合分析。

### **整体业务流程概述**

这个业务流程描绘了PDA（手持数据终端）操作员在生产线完成产品包装后，如何将产品批量送交质量部门检验的全过程。它由三个函数串联而成：

1.  **第一步：验证产线 (`af_pda_ea_no_verify`)**
    *   操作员首先扫描所在产线的条码。
    *   系统调用此函数，验证产线编码是否真实、有效，为后续操作提供一个合法的“地点”上下文。

2.  **第二步：逐个扫描并校验外箱 (`af_jiaoyantiaoma_finish_io`)**
    *   操作员开始逐一扫描本次希望送检的所有产品的外箱条码。
    *   每扫描一个条码，系统就调用此函数进行 **实时前置校验**。此函数作为“守门员”，会检查条码状态、物料一致性、是否重复送检、是否有OQC不合格等一系列规则。只有通过校验的条码才会被加入到待提交列表。

3.  **第三步：批量提交生成单据 (`af_me_finish_io_h_scan`)**
    *   操作员扫描完所有外箱后，点击“提交”按钮。
    *   系统将待提交列表中的所有有效条码，一次性传递给此函数。
    *   此函数是核心的数据处理后台，它会 **批量创建**“生产完工入库单”和“完工检验单”，并将所有相关的条码明细关联到这些新生成的单据上，正式完成送检流程。

---

### **函数详细分析**

#### **1. `af_pda_ea_no_verify` (产线校验函数)**

*   **功能概述**: 这是一个简单的验证服务，用于确认PDA扫描的产线编码（`ea_no`）是否在 `ea` 表中存在且被标记为有效产线 (`is_line = true`)。
*   **业务逻辑**: 保证后续的所有操作都发生在一个已知的、正确的生产单元上，防止操作员选错产线。
*   **数据逻辑**:
    *   输入: `{ "datas": [{ "ea_no": "产线编码" }] }`
    *   处理: `SELECT` from `ea` table.
    *   输出: 成功则返回产线信息，失败则返回错误提示。
*   **代码逻辑**: 使用 `IF NOT EXISTS` 进行判断，逻辑清晰直接。

---

#### **2. `af_jiaoyantiaoma_finish_io` (送检前置校验函数)**

*   **功能概述**: 流程的“守门员”，对每一个扫描的外箱条码进行严格的实时验证，确保其符合送检条件。
*   **核心业务校验点**:
    *   **条码有效性**: 存在、类型为外箱、状态为“确认”。
    *   **数据一致性**: 条码的物料/工单信息与主数据一致。
    *   **流程完整性**: 包装已满箱、未重复送检（重工单除外）。
    *   **质量合规性**: 未在OQC过程检验中被判定为“批退”。
*   **数据逻辑**: 广泛关联 `wm_sn`, `mo`, `me_finish_io`, `qm_si_lot_h` 等多个核心表进行交叉验证。
*   **代码逻辑**: 采用串行 `IF...THEN` 结构，一旦有任何校验失败则立即返回错误并中断，效率高，反馈明确。

---

#### **3. `af_me_finish_io_h_scan` (批量完工送检处理函数)**

*   **功能概述**: 这是流程的最终执行者。它接收所有已通过前置校验的条码列表，然后批量生成“生产完工单”和“质量检验单”。
*   **业务逻辑分析**:
    1.  **批量处理**: 首先将传入的条码列表存入一个临时表 `temp_table_scwgsj`。这是处理批量数据的最佳实践，极大提升了性能。
    2.  **周期管控 (华为产品)**: 在处理前，会检查所有待提交的条码是否属于同一个生产周期（通过解析 `sn_no` 字符串判断）。如果来自多个周期，则会报错，要求用户分开提交。这是一个非常具体的客户化业务规则。
    3.  **分组生成单据**: 核心逻辑是 **按（产线、工单、物料）对条码进行分组**。它会 `LOOP` 遍历每一个不同的组，为每一个组都创建一套独立的“生产完工单” (`me_finish_io_h`) 和“质量检验单” (`qm_si_lot_h`)。
    4.  **关联内外码**: 在生成检验单明细 (`qm_si_lot_b_sn`) 时，使用 `UNION ALL` 将外箱本身及其包含的所有内包装/产品码都添加到检验清单中，确保检验的全面性。
    5.  **批次号**: `_lot_no` 被固定设置为 `'MES888888'`，这可能是一个特定的业务需求，表示这是一个由MES系统生成的内部流转批次。
*   **数据逻辑分析**:
    *   **输入**: `{ "user_no": "...", "datas": [{ "sn_no": "...", "ea_no": "..." }, ...] }`
    *   **核心表**:
        *   **写入**: `me_finish_io_h`, `me_finish_io`, `qm_si_lot_h`, `qm_si_lot_b_sn`。
        *   **读取**: `wm_sn`, `mo`, `pd_part`, `ea`, `ss_user`。
    *   **数据流**: `输入JSON -> 临时表 -> 按组循环 -> 批量INSERT`。
*   **代码逻辑分析**:
    *   使用 `json_to_recordset` 将 JSON 数组高效地转换为行记录。
    *   `CREATE TEMP TABLE` 的使用是代码的一个亮点。
    *   `FOR LOOP` 循环处理分组逻辑。
    *   `INSERT INTO ... SELECT ...` 的模式被用来从关联表中高效地批量插入数据。

---

### **核心表关联逻辑 (完整流程)**

```mermaid
graph TD
    subgraph "输入与校验 (af_jiaoyantiaoma_finish_io)"
        EA_IN[("ea 产线")]
        SN_IN[("wm_sn 外箱")]
        MO_IN[("mo 工单")]
        MFI_IN[("me_finish_io 完工单")]
        QSLH_IN[("qm_si_lot_h 检验批")]
    end
    
    subgraph "批量创建 (af_me_finish_io_h_scan)"
        MFH_OUT[me_finish_io_h 完工单头]
        MFI_OUT[me_finish_io 完工单明细]
        QSLH_OUT[qm_si_lot_h 检验批头]
        QSLBSN_OUT[qm_si_lot_b_sn 检验批明细]
    end
    
    subgraph "核心主数据"
        SN[wm_sn]
        MO[mo]
        PD[pd_part]
    end

    %% Flow
    SN_IN -- 校验 --> MO_IN & MFI_IN & QSLH_IN
    
    SN_IN -- "通过校验后" --> MFH_OUT
    
    %% Creation Logic
    MFH_OUT -- "创建" --> MFI_OUT
    MFH_OUT -- "下推" --> QSLH_OUT
    QSLH_OUT -- "创建" --> QSLBSN_OUT

    %% Master Data Links
    MO -- "关联" --> MFH_OUT
    PD -- "关联" --> MFH_OUT
    PD -- "关联" --> MFI_OUT
    PD -- "关联" --> QSLH_OUT

    SN -- "关联" --> MFI_OUT
    SN -- "关联" --> QSLBSN_OUT
    
    %% Styling
    style MFH_OUT fill:#99ccff
    style QSLH_OUT fill:#99ccff
    style MFI_OUT fill:#c3e6ff
    style QSLBSN_OUT fill:#c3e6ff

```

---

## **深度分析补充**

### **2. 数据逻辑分析**

#### **2.1 数据流向分析**

```mermaid
graph LR
    subgraph "输入数据"
        A[PDA扫描数据] --> B[JSON格式]
        B --> C[产线编码ea_no]
        B --> D[外箱条码sn_no]
        B --> E[用户信息user_no]
    end

    subgraph "数据校验层"
        F[产线有效性校验]
        G[条码状态校验]
        H[业务规则校验]
        I[质量状态校验]
    end

    subgraph "数据存储层"
        J[完工单头表]
        K[完工单明细表]
        L[检验批头表]
        M[检验批明细表]
    end

    C --> F
    D --> G --> H --> I
    F --> J
    G --> K
    H --> L
    I --> M
```

#### **2.2 核心数据实体关系**

| 表名 | 作用 | 关键字段 | 数据流向 |
|------|------|----------|----------|
| `ea` | 产线主数据 | `ea_no`, `is_line`, `ea_type3` | 输入验证 |
| `wm_sn` | 条码主数据 | `sn_no`, `sn_type`, `sn_status`, `mo_no` | 核心校验源 |
| `mo` | 工单主数据 | `mo_no`, `part_no`, `workshop_no`, `mo_type` | 业务关联验证 |
| `me_finish_io_h` | 完工单头 | `me_finish_io_no`, `finish_io_status` | 输出生成 |
| `me_finish_io` | 完工单明细 | `sn_no`, `finish_io_qty_ok` | 输出生成 |
| `qm_si_lot_h` | 检验批头 | `si_lot_h_no`, `si_lot_move_type` | 输出生成 |
| `qm_si_lot_b_sn` | 检验批明细 | `sn_no`, `si_conclusion` | 输出生成 |

#### **2.3 数据一致性保证机制**

1. **物料编码一致性**: 条码物料 vs 工单物料
2. **产线归属一致性**: 条码产线 vs 操作产线
3. **状态流转一致性**: 条码状态必须为"确认"(110)
4. **包装完整性**: `pack_qty_used = part_qty`
5. **重复性控制**: 防止同一条码多次送检

### **3. 业务逻辑分析**

#### **3.1 业务规则矩阵**

| 业务场景 | 校验规则 | 错误处理 | 业务影响 |
|----------|----------|----------|----------|
| **产线验证** | `ea.is_line = true` | 立即返回错误 | 阻止非生产线操作 |
| **条码类型** | `sn_type = '40'` (外箱) | 提示条码类型错误 | 确保只处理外箱 |
| **条码状态** | `sn_status = '110'` (确认) | 提示状态不符 | 保证流程完整性 |
| **包装完成** | `pack_qty_used = part_qty` | 阻止未完成包装 | 确保包装质量 |
| **重工单处理** | 首字符为'F'的特殊逻辑 | 允许重复送检 | 支持返工流程 |
| **江西产线** | `ea_type3 like '%江西%'` | 委外分厂校验 | 地域化管理 |
| **华为产品** | 周期码解析校验 | 分批次提交 | 客户化需求 |
| **OQC不合格** | 批退状态检查 | 阻止不合格品 | 质量管控 |

#### **3.2 特殊业务逻辑深度解析**

##### **3.2.1 重工单逻辑 (F开头工单)**
```sql
-- 重工单允许同一条码多次送检的业务逻辑
_mo_no_f := substring(_mo_no,1,1);
if(_mo_no_f = 'F')then
    -- 只校验：重工单+条码的组合是否重复
    -- 允许同一条码在不同重工单中送检
end if;
```
**业务价值**: 支持产品返工流程，提高生产灵活性

##### **3.2.2 江西产线委外校验**
```sql
-- 江西产线需要额外的委外分厂校验
if exists(select 1 from ea where ea_no = _ea_no and ea_type3 like '%江西%')then
    -- 校验工单的委外分厂产线与当前产线一致性
end if;
```
**业务价值**: 支持委外生产管理，确保地域化生产合规

##### **3.2.3 华为产品周期管控**
```sql
-- 华为产品必须按生产周期分批提交
select count(prod_cycle) into _prod_cycle_cnt
from (select distinct substring(split_part(sn_no,'/021748',2),1,4) as prod_cycle
      from temp_table_scwgsj) tt;
```
**业务价值**: 满足客户特殊要求，提高产品追溯精度

#### **3.3 业务状态流转图**

```mermaid
stateDiagram-v2
    [*] --> 产线验证
    产线验证 --> 条码扫描: 产线有效
    产线验证 --> [*]: 产线无效

    条码扫描 --> 条码校验: 扫描成功
    条码校验 --> 待提交列表: 校验通过
    条码校验 --> 条码扫描: 校验失败

    待提交列表 --> 批量提交: 用户确认
    批量提交 --> 单据生成: 数据处理
    单据生成 --> 送检完成: 成功
    单据生成 --> 待提交列表: 失败回滚

    送检完成 --> [*]

### **4. 代码逻辑分析**

#### **4.1 代码架构设计模式**

##### **4.1.1 函数职责分离 (Single Responsibility Principle)**
- `af_pda_ea_no_verify`: 专注产线验证
- `af_jiaoyantiaoma_finish_io`: 专注条码校验
- `af_me_finish_io_h_scan`: 专注数据处理

##### **4.1.2 错误处理模式**
​```sql
-- 统一的错误返回格式
_results := row('false', '错误信息');
return to_json(_results);

-- 成功返回格式
_results := row('true', '成功信息', '数据');
return to_json(_results);
```

##### **4.1.3 性能优化策略**

1. **临时表批量处理**
```sql
-- 使用临时表避免多次JSON解析
create temp table temp_table_scwgsj as (
    select a.ea_no, a.sn_no, b.mo_no, b.part_no, b.part_qty
    from json_to_recordset(json(_scan)) as a(sn_no text, ea_no text)
    left join wm_sn b on b.sn_no = a.sn_no
);
```

2. **批量INSERT优化**
```sql
-- 使用INSERT INTO ... SELECT避免循环插入
insert into me_finish_io (...)
select af_auid(), _me_finish_io_no, ...
from temp_table_scwgsj a
left join wm_sn ws on ws.sn_no = a.sn_no;
```

3. **EXISTS vs IN 优化**
```sql
-- 使用EXISTS提高查询性能
if exists(select 1 from wm_sn where sn_no = _sn_no) then
```

#### **4.2 代码质量分析**

##### **4.2.1 优点**
- ✅ 使用存储过程提高执行效率
- ✅ JSON数据格式标准化
- ✅ 事务性操作保证数据一致性
- ✅ 详细的错误信息便于调试
- ✅ 临时表提高批量处理性能

##### **4.2.2 潜在改进点**
- ⚠️ 硬编码值较多 (如 `'MES888888'`)
- ⚠️ 缺少事务回滚机制
- ⚠️ 部分SQL语句可以进一步优化
- ⚠️ 错误日志记录不够完善

#### **4.3 关键代码片段解析**

##### **4.3.1 JSON数据解析技巧**
```sql
-- 多层JSON解析
jsonDatas := json(datas);
jsonDatas := jsonDatas->'datas'->0;
_ea_no := jsonDatas->>'ea_no';
```

##### **4.3.2 动态SQL构建**
```sql
-- 使用json_to_recordset动态构建临时表
select a.ea_no, a.sn_no from
json_to_recordset(json(_scan)) as a(sn_no text, ea_no text)
```

##### **4.3.3 条件分支优化**
```sql
-- 使用CASE WHEN简化条件判断
select case when coalesce(mo_type,'')='20' then '1'
       when coalesce(mo_type,'')='70' then '2'
       else '' end into _mo_type

### **5. 表关联逻辑分析**

#### **5.1 核心表关联关系图**

​```mermaid
erDiagram
    EA ||--o{ WM_SN : "产线关联"
    MO ||--o{ WM_SN : "工单关联"
    PD_PART ||--o{ WM_SN : "物料关联"
    PD_PART ||--o{ MO : "物料关联"

    WM_SN ||--o{ ME_FINISH_IO : "条码关联"
    ME_FINISH_IO_H ||--o{ ME_FINISH_IO : "单头单身"

    QM_SI_LOT_H ||--o{ QM_SI_LOT_B_SN : "检验批关联"
    WM_SN ||--o{ QM_SI_LOT_B_SN : "条码检验"

    MO_RMB ||--o{ MO_RMB_EA : "委外关联"
    EA ||--o{ MO_RMB_EA : "产线委外"

    EA {
        varchar ea_no PK "产线编码"
        boolean is_line "是否产线"
        varchar ea_type3 "产线类型"
        varchar ea_name "产线名称"
    }

    WM_SN {
        varchar sn_no PK "条码"
        varchar sn_type "条码类型"
        varchar sn_status "条码状态"
        varchar mo_no FK "工单号"
        varchar part_no FK "物料编码"
        numeric part_qty "数量"
        varchar sn_pack_50 "外箱关联"
    }

    MO {
        varchar mo_no PK "工单号"
        varchar part_no FK "物料编码"
        varchar workshop_no "车间"
        varchar mo_type "工单类型"
        varchar mo_parent_top "父工单"
    }

    ME_FINISH_IO_H {
        varchar me_finish_io_id_h PK "完工单头ID"
        varchar me_finish_io_no "完工单号"
        varchar finish_io_status "状态"
        varchar mo_no FK "工单号"
    }

    QM_SI_LOT_H {
        varchar si_lot_h_id PK "检验批ID"
        varchar si_lot_h_no "检验批号"
        varchar si_lot_move_type "移动类型"
        varchar order_no FK "订单号"
    }
```

#### **5.2 关联逻辑详细分析**

##### **5.2.1 主数据关联验证**
```sql
-- 条码 -> 工单 -> 物料 三层关联验证
select ws.mo_no, part_no into _mo_no, _part_no_sn
from wm_sn ws where ws.sn_no = _sn_no;

select m.workshop_no, m.mo_parent_top, part_no
into _mo_ea_no, _mo_parent_top, _part_no_mo
from mo m where m.mo_no = _mo_no;

-- 物料编码一致性校验
if coalesce(_part_no_sn,'') <> coalesce(_part_no_mo,'') then
    -- 报错：标签物料与工单物料不一致
end if;
```

##### **5.2.2 委外分厂关联逻辑**
```sql
-- 江西产线的委外分厂校验
select 1 from mo_rmb b
left join mo_rmb_ea e on e.unode = b.unode
left join ea a on a.ea_id = e.ea_id
where b.mo_no = _mo_no and a.ea_no = _ea_no
```

##### **5.2.3 质量检验关联逻辑**
```sql
-- OQC过程检验不合格品校验
select 1 from qm_si_lot_h h
where h.order_no = _mo_no
  and h.si_lot_move_type = '303'
  and h.si_conclusion_name = '不合格'
```

##### **5.2.4 包装层级关联逻辑**
```sql
-- 外箱与内包装关联
union all
select ... from temp_table_scwgsj a
join wm_sn ws on a.sn_no = ws.sn_pack_50  -- 外箱关联
```

#### **5.3 数据完整性约束**

| 约束类型 | 表关联 | 业务规则 | 技术实现 |
|----------|--------|----------|----------|
| **引用完整性** | `wm_sn.mo_no -> mo.mo_no` | 条码必须有对应工单 | EXISTS校验 |
| **业务完整性** | `wm_sn.part_no = mo.part_no` | 条码物料与工单物料一致 | 交叉验证 |
| **状态完整性** | `wm_sn.sn_status = '110'` | 只有确认状态可送检 | 状态校验 |
| **数量完整性** | `pack_qty_used = part_qty` | 包装数量必须完整 | 数量校验 |
| **唯一性约束** | `me_finish_io.sn_no` | 防止重复送检 | 历史记录校验 |

#### **5.4 性能优化的关联策略**

1. **索引优化建议**
```sql
-- 建议创建的复合索引
CREATE INDEX idx_wm_sn_composite ON wm_sn(sn_no, sn_type, sn_status);
CREATE INDEX idx_me_finish_io_sn ON me_finish_io(sn_no, finish_io_status);
CREATE INDEX idx_qm_si_lot_order ON qm_si_lot_h(order_no, si_lot_move_type);
```

2. **关联查询优化**
```sql
-- 使用LEFT JOIN避免笛卡尔积
left join wm_sn ws on ws.sn_no = a.sn_no
left join pd_part pp on pp.part_no = ws.part_no
```

### **6. 系统改进建议**

#### **6.1 架构层面**
- 🔧 增加配置表管理硬编码值
- 🔧 实现完整的事务回滚机制
- 🔧 添加详细的操作日志记录
- 🔧 实现异步处理提高响应速度

#### **6.2 业务层面**
- 📋 增加批量操作的进度反馈
- 📋 支持部分成功的处理策略
- 📋 增加更灵活的业务规则配置
- 📋 支持更多客户化需求

#### **6.3 技术层面**
- ⚡ 优化大批量数据处理性能
- ⚡ 增加并发控制机制
- ⚡ 实现更好的错误恢复策略
- ⚡ 添加监控和告警机制

---

## **总结**

这个PDA MES生产完工入库送检系统是一个设计完善的制造业质量管控系统，具有以下特点：

- **业务完整性**: 覆盖了从产线验证到批量送检的完整流程
- **数据一致性**: 通过多层校验确保数据质量
- **性能优化**: 使用临时表和批量处理提高效率
- **扩展性**: 支持客户化需求和特殊业务场景
- **可维护性**: 函数职责清晰，代码结构合理

该系统为制造企业提供了可靠的产品质量追溯和管控能力，是MES系统中的重要组成部分。
```

```
