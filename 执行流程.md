调用流程：

```mermaid
graph TD
 A[成品入库]

A-->B[非序列号管控]

A-->C[序列号管控]

B-->D[MES>根据工单号生成>生产工单生成入库单.无序列号管控]

C-->E[PDA>MES生产完工入库.送检]

D-->G[MES>生产完成品入库查询]

D-->J[MES>生产完工检验]

D-->F[接口MES->SAP生产入库申请单]

E-->G[MES>生产完成品入库查询]

E-->J[MES>生产完工检验]

E-->F[接口MES->SAP生产入库申请单]

J-->K[检验完成打印入库单]

K-->L[有序列号>PDA>仓库接收]

K-->Z[无序列号>PDA>仓库接收.无序列号管控]

L-->X[接口MES->SAP生产入库单]

Z-->X[接口MES->SAP生产入库单]

X-->V[成品入库完成]

```

---

### 核心表关联关系

```mermaid
graph TD
    %% Define subgraphs for different modules/processes
    subgraph "核心主数据"
        MO[mo 工单]
        PD[pd_part 物料]
        SN[wm_sn 序列号]
    end

    subgraph "生产完工与检验流程"
        MFH[me_finish_io_h 生产完工头]
        MFI[me_finish_io 生产完工明细]
        QSLH[qm_si_lot_h 检验批头]
        QSLB[qm_si_lot_b_sn 检验批序列号]
        
        MFH --> |"一对多"| MFI
        QSLH --> |"一对多"| QSLB
    end

    subgraph "分厂/委外暂收流程"
        WTR[wm_temp_receipt 暂收单头]
        WTRB[wm_temp_receipt_b 暂收单明细]
        WTRSN[wm_temp_receipt_sn_part 暂收单序列号]
        
        WTR --> WTRB
        WTRB --> WTRSN
    end
    
    subgraph "销售退货流程"
        CRH[szjy_mes_cr_rtn_h 销售退货头]
        CRB[szjy_mes_cr_rtn_b 销售退货明细]
        CRSN[szjy_mes_cr_rtn_sn_part 销售退货序列号]
        
        CRH --> CRB
        CRB --> CRSN
    end

    %% Define inter-subgraph relationships
    MO -- "生成" --> MFH
    PD -- "关联" --> MFH
    PD -- "关联" --> QSLH
    PD -- "关联" --> CRB
    
    SN -- "关联" --> MFI
    SN -- "关联" --> QSLB
    SN -- "关联" --> WTRSN
    SN -- "关联" --> CRSN
    
    MFH -- "下推生成(检验)" --> QSLH
    MFH -- "下推生成(暂收)" --> WTR
    WTR -- "下推生成(检验)" --> QSLH
    
    %% Apply styling similar to the template
    style MO fill:#ff9999
    style PD fill:#ff9999
    style SN fill:#ff9999
    style MFH fill:#99ccff
    style WTR fill:#99ccff
    style CRH fill:#99ccff
    style QSLH fill:#99ff99
```

![1751450837337](D:\金洋\成品出入库记录\入库\assets\1751450837337.png)