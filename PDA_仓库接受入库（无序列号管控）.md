```sql
-- DROP FUNCTION public.af_pda_wms_prod_inbound_no_sn(varchar);

CREATE OR REPLACE FUNCTION public.af_pda_wms_prod_inbound_no_sn(datas character varying)
 RETURNS character varying
 LANGUAGE plpgsql
AS $function$
/*
 * 功能：  仓库入库（无序列号管控产品）
 * 描述：  
 * 时间：  2024/10
 * 开发者：
 * 数据：  
 */
	declare
		json_datas json;
		_user_id text;
		_user_no text;
		_user_name text;
		_host text;

		_bill_no text;
		_bill_status text;
		_mo_no text;
		_part_no text;
		_part_qty text;

		_inbound_org text;
		_temp_receipt_id text;

		_err_msg_text text;
		_err_pg_detail text;
		_err_msg text;
		res returntype;
	BEGIN
		json_datas := json(datas);
		_user_no := json_datas->>'user_no';

		json_datas := json(json_datas->'datas'->0);
		_bill_no := json_datas->>'bill_no';	

		select user_id,user_name into _user_id,_user_name from ss_user where user_no=_user_no;

		if not exists(select 1 from ss_user where user_no=_user_no) then
			res := row('false', 'PDA关联AX用户不存在。');
			return to_json(res);
		end if;
		
		if not exists(select 1 from me_finish_io_h where me_finish_io_no=_bill_no) then
			_err_msg := format('扫描入库【%s】不存在。', _bill_no);
			res := row('false', _err_msg);
			return to_json(res);
		end if;		

		if not exists(select 1 from qm_si_lot_h where move_order_no=_bill_no and si_conclusion_name in ('合格','特采')) then
			res := row('false', '此入库单的完工检验单未做检验结论 或者结论不是合格。');
			return to_json(res);
		end if;

		if exists(select distinct 1 from me_finish_io where me_finish_io_no=_bill_no and coalesce(sn_no, '')!='') then
			res := row('false', '此入库单为序列号管控产品，不能使用无序列号管控接收入库.');
			return to_json(res);
		end if;

		if not exists(select 1 from me_finish_io_h where me_finish_io_no=_bill_no and coalesce(me_finish_io_rmk4,'')='') then
			select me_finish_io_rmk4 into _bill_status from me_finish_io_h where me_finish_io_no=_bill_no;
			_err_msg := format('扫描入库【%s】单状态为【%s】，不能入库。', _bill_no,_bill_status);
			res := row('false', _err_msg);
			return to_json(res);
		end if;

		-----------------------------------------------------------------------------------------

		select (case when workshop_no='J129' then 'JXML' when workshop_no='J171' then 'JXRS' else workshop_no end) into _inbound_org 
		from me_finish_io_h where me_finish_io_no=_bill_no;

		if coalesce(_inbound_org, '')='' then
			res := row('false', '此入库单没有指定入库组织，不能入库');
			return to_json(res);
		end if;

		-----------------------------------------------------------------------------------------
		/*
		if strpos(_inbound_org,'JX')=1 then

			_temp_receipt_id := af_auid();
		
			insert into public.wm_temp_receipt
			(temp_receipt_id, temp_receipt_status, temp_receipt_no, temp_receipt_type, delivery_no, factory_no, temp_receipt_rmk01, temp_receipt_rmk02, temp_receipt_rmk03, temp_receipt_rmk04, temp_receipt_rmk05, temp_receipt_rmk06, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host)
			values(_temp_receipt_id, '110', af_ss_no_generate('wm_temp_receipt_no'), '', _bill_no, 'comlink', '暂收完成', '', '', '', '', '', localtimestamp, _user_id, _user_no, _user_name, '', localtimestamp, _user_id, _user_no, _user_name, '');

			insert into public.wm_temp_receipt_b
			(temp_receipt_b_id, temp_receipt_id, part_no, part_name, part_spec, part_unit, part_qty_plan, part_qty_real, mo_no, temp_receipt_rmk01, temp_receipt_rmk02, temp_receipt_rmk03, temp_receipt_rmk04, temp_receipt_rmk05, temp_receipt_rmk06, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host)
			select af_auid(), _temp_receipt_id, b.part_no, c.part_name, c.part_spec, c.part_unit, a.finish_io_qty_ok, a.finish_io_qty_ok, a.mo_no, '暂收完成', '', '', '', '', '', localtimestamp, _user_id, _user_no, _user_name, '', localtimestamp, _user_id, _user_no, _user_name, ''
			from me_finish_io_h a
			left join mo b on b.mo_no=a.mo_no
			left join pd_part c on c.part_no=b.part_no
			where me_finish_io_no=_bill_no;
			
			update public.me_finish_io_h set me_finish_io_rmk4='暂收完成',
				upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
			where me_finish_io_no=_bill_no;

			update public.me_finish_io set me_finish_io_rmk4='暂收完成',
				upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
			where me_finish_io_no=_bill_no;

		else
			update public.me_finish_io_h set me_finish_io_rmk4='入库完成',
				upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
			where me_finish_io_no=_bill_no;

			update public.me_finish_io set me_finish_io_rmk4='入库完成',
				upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
			where me_finish_io_no=_bill_no;
		end if;
		*/
		------2025/04/23要求取消分厂回来非华为产品暂收功能---------
			update public.me_finish_io_h set me_finish_io_rmk4='入库完成',
				upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
			where me_finish_io_no=_bill_no;

			update public.me_finish_io set me_finish_io_rmk4='入库完成',
				upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
			where me_finish_io_no=_bill_no;
		--------------------------------------------------------------------------------------------

		res := row('true', '--入库完成--');
		return to_json(res);

	EXCEPTION WHEN OTHERS THEN 
		GET STACKED DIAGNOSTICS 
			_err_msg_text = MESSAGE_TEXT,
			_err_pg_detail = PG_EXCEPTION_DETAIL;
	
		_err_msg := format('错误信息:%s,详情:%s',_err_msg_text,_err_msg_text);
		res := row('false',_err_msg);
		return to_json(res);	
	END;
$function$
;

```

---

## **PDA仓库接受入库（无序列号管控）系统深度分析报告**

### **1. 系统概述**

这是一个专门针对无序列号管控产品的PDA仓库入库系统，采用单函数架构，实现了简化的入库流程。该系统是对有序列号管控入库系统的补充，体现了差异化产品管理策略。

#### **业务场景**
- **适用产品**: 无序列号管控的批量生产产品
- **使用场景**: 仓库人员扫描入库单号完成批量入库确认
- **核心价值**: 简化操作流程、提高批量产品入库效率
- **业务特点**: 以单据为单位进行批量确认，无需逐个扫描产品

#### **系统定位**
- **功能定位**: 有序列号管控系统的简化版本
- **业务补充**: 覆盖不同产品类型的管控需求
- **流程优化**: 针对批量产品的高效入库处理

---

## **2. 数据逻辑分析**

### **2.1 数据流向架构图**

```mermaid
graph TD
    A[PDA扫描入库单号] --> B[用户验证]
    B --> C[入库单存在性验证]
    C --> D[检验结论验证]
    D --> E[序列号管控验证]
    E --> F[单据状态验证]
    F --> G[组织类型识别]
    G --> H[批量状态更新]
    H --> I[入库完成]

    subgraph "数据校验层"
        J[用户存在性]
        K[入库单有效性]
        L[质检合格性]
        M[产品类型匹配]
        N[状态可操作性]
    end

    B --> J
    C --> K
    D --> L
    E --> M
    F --> N

    subgraph "业务规则层"
        O[组织代码映射]
        P[状态流转规则]
        Q[批量更新策略]
    end

    G --> O
    H --> P
    H --> Q
```

![1751455772130](D:\金洋\成品出入库记录\入库\assets\1751455772130.png)

### 2.2 核心数据实体关系**

| 表名 | 作用 | 关键字段 | 数据流向 |
|------|------|----------|----------|
| `me_finish_io_h` | 完工单头 | `me_finish_io_no`, `workshop_no`, `me_finish_io_rmk4` | 主控制表 |
| `me_finish_io` | 完工单明细 | `me_finish_io_no`, `sn_no`, `me_finish_io_rmk4` | 明细状态控制 |
| `qm_si_lot_h` | 检验批头 | `move_order_no`, `si_conclusion_name` | 质量控制 |
| `ss_user` | 用户信息 | `user_no`, `user_id`, `user_name` | 操作人员验证 |

### **2.3 数据输入输出对比分析**

#### **输入数据结构对比**

| 字段 | 有序列号管控 | 无序列号管控 | 差异说明 |
|------|-------------|-------------|----------|
| `user_no` | ✅ 必需 | ✅ 必需 | 相同 |
| `bill_no` | ✅ 必需 | ✅ 必需 | 相同 |
| `sn_no` | ✅ 必需 | ❌ 不需要 | **核心差异** |

#### **数据处理粒度对比**

| 处理维度 | 有序列号管控 | 无序列号管控 |
|----------|-------------|-------------|
| **处理单位** | 单个序列号 | 整个入库单 |
| **操作频次** | 逐个扫描 | 一次性确认 |
| **数据量** | 精确到件 | 批量处理 |
| **复杂度** | 高 | 低 |

### **2.4 数据状态流转逻辑**

#### **简化的状态流转**
```sql
-- 统一状态更新（2025/04/23后简化）
me_finish_io_h.me_finish_io_rmk4: '' → '入库完成'
me_finish_io.me_finish_io_rmk4: '' → '入库完成'
```

#### **历史状态流转（已注释）**
```sql
-- 原有的分组织处理逻辑（已废弃）
if strpos(_inbound_org,'JX')=1 then
    -- 江西分厂：暂收完成
    me_finish_io_rmk4: '' → '暂收完成'
else
    -- 本厂：入库完成
    me_finish_io_rmk4: '' → '入库完成'
end if;

---

## **3. 业务逻辑分析**

### **3.1 业务流程对比分析**

#### **有序列号 vs 无序列号业务流程对比**

​```mermaid
graph LR
    subgraph "有序列号管控流程"
        A1[扫描入库单] --> A2[扫描产品条码]
        A2 --> A3[组织类型判断]
        A3 --> A4[调用子函数]
        A4 --> A5[逐个处理]
        A5 --> A6[状态更新]
    end

    subgraph "无序列号管控流程"
        B1[扫描入库单] --> B2[批量验证]
        B2 --> B3[组织识别]
        B3 --> B4[批量更新]
        B4 --> B5[入库完成]
    end
```

#### **业务复杂度对比**

| 复杂度维度 | 有序列号管控 | 无序列号管控 | 简化程度 |
|-----------|-------------|-------------|----------|
| **函数数量** | 4个函数协同 | 1个函数独立 | 75%简化 |
| **验证步骤** | 7个验证点 | 5个验证点 | 28%简化 |
| **处理分支** | 多分支处理 | 单一流程 | 80%简化 |
| **状态管理** | 复杂状态流转 | 简单状态更新 | 70%简化 |

### **3.2 业务规则矩阵**

| 业务环节 | 验证规则 | 错误处理 | 业务影响 |
|----------|----------|----------|----------|
| **用户验证** | PDA用户在AX系统存在 | 立即返回错误 | 确保操作权限 |
| **入库单验证** | 入库单必须存在 | 提示单据不存在 | 保证单据有效性 |
| **检验验证** | 必须有合格或特采结论 | 阻止未检验产品入库 | 确保质量合规 |
| **产品类型验证** | 确保非序列号管控产品 | 防止错误使用接口 | 保证业务逻辑正确 |
| **状态验证** | 单据状态允许入库 | 防止重复操作 | 确保流程完整性 |

### **3.3 特殊业务逻辑深度解析**

#### **3.3.1 序列号管控验证逻辑**
```sql
-- 关键业务规则：防止序列号产品误用
if exists(select distinct 1 from me_finish_io
          where me_finish_io_no=_bill_no
          and coalesce(sn_no, '')!='') then
    res := row('false', '此入库单为序列号管控产品，不能使用无序列号管控接收入库.');
end if;
```
**业务价值**: 确保不同产品类型使用正确的入库接口，防止业务混乱

#### **3.3.2 检验结论强制验证**
```sql
-- 质量管控：必须有检验结论
if not exists(select 1 from qm_si_lot_h
              where move_order_no=_bill_no
              and si_conclusion_name in ('合格','特采')) then
    res := row('false', '此入库单的完工检验单未做检验结论 或者结论不是合格。');
end if;
```
**业务价值**: 确保所有入库产品都经过质量检验，维护产品质量标准

#### **3.3.3 业务流程简化演进**
```sql
-- 2025/04/23业务需求变更
-- 原有逻辑：分组织处理（已注释）
-- 新逻辑：统一处理，取消暂收功能
update me_finish_io_h set me_finish_io_rmk4='入库完成'
update me_finish_io set me_finish_io_rmk4='入库完成'
```
**业务价值**: 简化操作流程，提高入库效率，减少业务复杂度

### **3.4 业务流程时序图**

```mermaid
sequenceDiagram
    participant User as 仓库操作员
    participant PDA as PDA设备
    participant System as 无序列号入库系统
    participant QM as 质检系统
    participant WMS as 库存系统

    User->>PDA: 扫描入库单号
    PDA->>System: 调用无序列号入库函数

    System->>System: 用户验证
    System->>System: 入库单存在性验证
    System->>QM: 检验结论验证
    QM-->>System: 返回检验状态

    alt 检验合格
        System->>System: 序列号管控验证
        System->>System: 单据状态验证
        System->>System: 组织类型识别
        System->>WMS: 批量状态更新
        WMS-->>System: 更新成功
        System-->>PDA: 返回入库完成
        PDA-->>User: 显示操作成功
    else 检验不合格
        System-->>PDA: 返回错误信息
        PDA-->>User: 显示错误提示
    end


```

#### **防御式编程模式**
```sql
-- 多层验证确保数据安全
1. 用户验证
2. 入库单验证
3. 检验结论验证
4. 产品类型验证
5. 状态验证
```

### **4.2 关键代码逻辑解析**

#### **4.2.1 JSON数据解析简化**
```sql
-- 简化的数据解析（无需sn_no）
json_datas := json(datas);
_user_no := json_datas->>'user_no';
json_datas := json(json_datas->'datas'->0);
_bill_no := json_datas->>'bill_no';
```

#### **4.2.2 组织类型识别复用**
```sql
-- 复用有序列号系统的组织映射逻辑
select (case when workshop_no='J129' then 'JXML'
             when workshop_no='J171' then 'JXRS'
             else workshop_no end) into _inbound_org
```

#### **4.2.3 批量状态更新策略**
```sql
-- 简化的批量更新（无条件分支）
update me_finish_io_h set me_finish_io_rmk4='入库完成' where me_finish_io_no=_bill_no;
update me_finish_io set me_finish_io_rmk4='入库完成' where me_finish_io_no=_bill_no;
```

### **4.3 代码质量分析**

#### **优点**
- ✅ **逻辑清晰**: 单一函数，流程简单明了
- ✅ **验证完整**: 多重验证确保数据安全
- ✅ **错误处理**: 详细的错误信息便于问题定位
- ✅ **代码复用**: 复用组织映射等公共逻辑

#### **潜在改进点**
- ⚠️ **代码重复**: 与有序列号系统存在重复的验证逻辑
- ⚠️ **硬编码**: 组织映射规则硬编码在代码中
- ⚠️ **功能退化**: 注释掉的暂收功能代码应该清理
- ⚠️ **扩展性**: 缺少配置化的业务规则管理

#### **代码优化建议**
```sql
-- 建议提取公共验证函数
CREATE FUNCTION validate_inbound_prerequisites(_bill_no text, _user_no text)
RETURNS json AS $$
BEGIN
    -- 公共验证逻辑
END;
$$ LANGUAGE plpgsql;

-- 建议清理废弃代码
-- 删除注释掉的暂收功能代码块
```

### **4.4 代码演进历史分析**

#### **功能演进轨迹**
```sql
-- 第一阶段：完整功能（支持暂收）
if strpos(_inbound_org,'JX')=1 then
    -- 暂收逻辑
else
    -- 直接入库逻辑
end if;

-- 第二阶段：功能简化（2025/04/23）
-- 统一为直接入库，注释暂收功能

-- 第三阶段：代码清理（建议）
-- 删除注释代码，保持代码整洁
```

#### **业务需求变更影响**
- **需求变更**: 取消分厂非华为产品暂收功能
- **代码影响**: 大段代码被注释，逻辑简化
- **维护问题**: 注释代码影响可读性

---

## **5. 表关联逻辑分析**

### **5.1 核心表关联关系图**

```mermaid
erDiagram
    ME_FINISH_IO_H ||--o{ ME_FINISH_IO : "完工单头身关联"
    ME_FINISH_IO_H ||--o{ QM_SI_LOT_H : "检验单关联"
    SS_USER ||--o{ ME_FINISH_IO_H : "操作人员关联"

    ME_FINISH_IO_H {
        varchar me_finish_io_no PK "完工单号"
        varchar workshop_no "车间代码"
        varchar me_finish_io_rmk4 "入库状态"
        varchar mo_no "工单号"
        varchar part_no "物料编码"
        timestamp upd_time "更新时间"
        varchar upd_user "更新用户"
    }

    ME_FINISH_IO {
        varchar me_finish_io_no FK "完工单号"
        varchar sn_no "序列号"
        varchar me_finish_io_rmk4 "入库状态"
        numeric finish_io_qty_ok "合格数量"
        timestamp upd_time "更新时间"
    }

    QM_SI_LOT_H {
        varchar move_order_no FK "移动单号"
        varchar si_conclusion_name "检验结论"
        varchar si_lot_move_type "移动类型"
        timestamp crt_time "创建时间"
    }

    SS_USER {
        varchar user_no PK "用户编号"
        varchar user_id "用户ID"
        varchar user_name "用户名称"
    }
```

![1751456069000](D:\金洋\成品出入库记录\入库\assets\1751456069000.png)

### **5.2 表关联逻辑详解**

#### **5.2.1 主表关联查询**
```sql
-- 用户信息关联
select user_id,user_name into _user_id,_user_name
from ss_user where user_no=_user_no;

-- 组织信息关联
select (case when workshop_no='J129' then 'JXML'
             when workshop_no='J171' then 'JXRS'
             else workshop_no end) into _inbound_org
from me_finish_io_h where me_finish_io_no=_bill_no;
```

#### **5.2.2 验证性关联查询**
```sql
-- 入库单存在性验证
select 1 from me_finish_io_h where me_finish_io_no=_bill_no

-- 检验结论验证
select 1 from qm_si_lot_h
where move_order_no=_bill_no
and si_conclusion_name in ('合格','特采')

-- 序列号管控验证
select distinct 1 from me_finish_io
where me_finish_io_no=_bill_no
and coalesce(sn_no, '')!=''
```

#### **5.2.3 状态更新关联**
```sql
-- 批量状态更新
update me_finish_io_h set me_finish_io_rmk4='入库完成'
where me_finish_io_no=_bill_no;

update me_finish_io set me_finish_io_rmk4='入库完成'
where me_finish_io_no=_bill_no;
```

### **5.3 数据一致性保证机制**

#### **业务完整性约束**
- **用户有效性**: 确保操作用户在系统中存在
- **单据有效性**: 确保入库单在系统中存在
- **质量合规性**: 确保产品通过质量检验
- **类型匹配性**: 确保产品类型与接口匹配

#### **状态一致性保证**
- **头身同步**: 同时更新单头和单身状态
- **操作追踪**: 记录操作人员和操作时间
- **状态流转**: 确保状态变更的合理性

---

## **6. 系统对比分析**

### **6.1 有序列号 vs 无序列号系统对比**

| 对比维度 | 有序列号管控系统 | 无序列号管控系统 | 差异分析 |
|----------|-----------------|-----------------|----------|
| **架构复杂度** | 4函数协同架构 | 单函数架构 | 简化75% |
| **输入参数** | 单号+序列号 | 仅单号 | 减少关键参数 |
| **处理粒度** | 逐个序列号 | 整单批量 | 粒度粗化 |
| **验证步骤** | 7个验证点 | 5个验证点 | 简化28% |
| **业务分支** | 多组织分流 | 统一处理 | 消除分支 |
| **状态管理** | 复杂流转 | 简单更新 | 大幅简化 |
| **扩展性** | 高扩展性 | 有限扩展 | 功能聚焦 |

### **6.2 适用场景分析**

#### **有序列号管控适用场景**
- 高价值产品（电子产品、精密设备）
- 需要精确追溯的产品
- 个性化定制产品
- 质量要求极高的产品

#### **无序列号管控适用场景**
- 标准化批量产品
- 成本敏感型产品
- 简单工艺产品
- 快速周转产品

### **6.3 业务价值对比**

| 价值维度 | 有序列号管控 | 无序列号管控 |
|----------|-------------|-------------|
| **追溯精度** | 精确到件 | 批次级别 |
| **操作效率** | 相对较低 | 高效率 |
| **管理成本** | 较高 | 较低 |
| **质量控制** | 精细化 | 标准化 |
| **适用范围** | 特定产品 | 通用产品 |

---

## **7. 系统优化建议**

### **7.1 代码优化**
- 🔧 **代码清理**: 删除注释掉的暂收功能代码
- 🔧 **函数提取**: 提取公共验证逻辑为独立函数
- 🔧 **配置化**: 将组织映射规则移到配置表
- 🔧 **标准化**: 统一错误处理和返回格式

### **7.2 功能增强**
- 📈 **批量验证**: 增加批量数据验证功能
- 📈 **操作日志**: 增加详细的操作日志记录
- 📈 **状态回滚**: 支持入库操作的撤销功能
- 📈 **数据分析**: 增加入库效率统计分析

### **7.3 架构优化**
- 🏗️ **接口统一**: 与有序列号系统统一接口规范
- 🏗️ **服务化**: 考虑微服务架构拆分
- 🏗️ **缓存优化**: 增加用户信息和基础数据缓存
- 🏗️ **监控告警**: 增加系统监控和异常告警

### **7.4 业务扩展**
- 🎯 **规则引擎**: 支持可配置的业务规则
- 🎯 **多组织**: 支持更多组织类型的扩展
- 🎯 **集成增强**: 与ERP、WMS等系统深度集成
- 🎯 **移动优化**: 优化PDA操作体验

---

## **总结**

这个PDA仓库接受入库（无序列号管控）系统是一个设计精简的批量入库解决方案，具有以下特点：

### **技术特色**
- **架构简化**: 单函数架构，逻辑清晰直接
- **验证完整**: 多重验证确保数据安全和业务正确性
- **批量处理**: 以单据为单位的批量确认模式
- **差异化设计**: 针对无序列号产品的专门优化

### **业务价值**
- **操作高效**: 一次扫描完成整单入库，大幅提升效率
- **流程简化**: 消除复杂的分支逻辑，降低操作复杂度
- **质量保证**: 强制检验验证确保产品质量合规
- **成本优化**: 适合批量标准化产品的成本控制需求

### **应用场景**
- 标准化批量生产的制造企业
- 成本敏感型产品的库存管理
- 快速周转产品的入库处理
- 简化流程的仓库作业管理

### **核心优势**
- **专业化**: 专门针对无序列号产品设计
- **高效率**: 批量处理模式大幅提升操作效率
- **低复杂度**: 简化的业务逻辑易于维护和使用
- **差异化**: 与有序列号系统形成互补的产品管理体系

该系统为企业提供了针对不同产品类型的差异化入库管理解决方案，是现代制造业精细化库存管理的重要组成部分。
```

```

