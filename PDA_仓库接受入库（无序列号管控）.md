```sql
-- DROP FUNCTION public.af_pda_wms_prod_inbound_no_sn(varchar);

CREATE OR REPLACE FUNCTION public.af_pda_wms_prod_inbound_no_sn(datas character varying)
 RETURNS character varying
 LANGUAGE plpgsql
AS $function$
/*
 * 功能：  仓库入库（无序列号管控产品）
 * 描述：  
 * 时间：  2024/10
 * 开发者：
 * 数据：  
 */
	declare
		json_datas json;
		_user_id text;
		_user_no text;
		_user_name text;
		_host text;

		_bill_no text;
		_bill_status text;
		_mo_no text;
		_part_no text;
		_part_qty text;

		_inbound_org text;
		_temp_receipt_id text;

		_err_msg_text text;
		_err_pg_detail text;
		_err_msg text;
		res returntype;
	BEGIN
		json_datas := json(datas);
		_user_no := json_datas->>'user_no';

		json_datas := json(json_datas->'datas'->0);
		_bill_no := json_datas->>'bill_no';	

		select user_id,user_name into _user_id,_user_name from ss_user where user_no=_user_no;

		if not exists(select 1 from ss_user where user_no=_user_no) then
			res := row('false', 'PDA关联AX用户不存在。');
			return to_json(res);
		end if;
		
		if not exists(select 1 from me_finish_io_h where me_finish_io_no=_bill_no) then
			_err_msg := format('扫描入库【%s】不存在。', _bill_no);
			res := row('false', _err_msg);
			return to_json(res);
		end if;		

		if not exists(select 1 from qm_si_lot_h where move_order_no=_bill_no and si_conclusion_name in ('合格','特采')) then
			res := row('false', '此入库单的完工检验单未做检验结论 或者结论不是合格。');
			return to_json(res);
		end if;

		if exists(select distinct 1 from me_finish_io where me_finish_io_no=_bill_no and coalesce(sn_no, '')!='') then
			res := row('false', '此入库单为序列号管控产品，不能使用无序列号管控接收入库.');
			return to_json(res);
		end if;

		if not exists(select 1 from me_finish_io_h where me_finish_io_no=_bill_no and coalesce(me_finish_io_rmk4,'')='') then
			select me_finish_io_rmk4 into _bill_status from me_finish_io_h where me_finish_io_no=_bill_no;
			_err_msg := format('扫描入库【%s】单状态为【%s】，不能入库。', _bill_no,_bill_status);
			res := row('false', _err_msg);
			return to_json(res);
		end if;

		-----------------------------------------------------------------------------------------

		select (case when workshop_no='J129' then 'JXML' when workshop_no='J171' then 'JXRS' else workshop_no end) into _inbound_org 
		from me_finish_io_h where me_finish_io_no=_bill_no;

		if coalesce(_inbound_org, '')='' then
			res := row('false', '此入库单没有指定入库组织，不能入库');
			return to_json(res);
		end if;

		-----------------------------------------------------------------------------------------
		/*
		if strpos(_inbound_org,'JX')=1 then

			_temp_receipt_id := af_auid();
		
			insert into public.wm_temp_receipt
			(temp_receipt_id, temp_receipt_status, temp_receipt_no, temp_receipt_type, delivery_no, factory_no, temp_receipt_rmk01, temp_receipt_rmk02, temp_receipt_rmk03, temp_receipt_rmk04, temp_receipt_rmk05, temp_receipt_rmk06, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host)
			values(_temp_receipt_id, '110', af_ss_no_generate('wm_temp_receipt_no'), '', _bill_no, 'comlink', '暂收完成', '', '', '', '', '', localtimestamp, _user_id, _user_no, _user_name, '', localtimestamp, _user_id, _user_no, _user_name, '');

			insert into public.wm_temp_receipt_b
			(temp_receipt_b_id, temp_receipt_id, part_no, part_name, part_spec, part_unit, part_qty_plan, part_qty_real, mo_no, temp_receipt_rmk01, temp_receipt_rmk02, temp_receipt_rmk03, temp_receipt_rmk04, temp_receipt_rmk05, temp_receipt_rmk06, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host)
			select af_auid(), _temp_receipt_id, b.part_no, c.part_name, c.part_spec, c.part_unit, a.finish_io_qty_ok, a.finish_io_qty_ok, a.mo_no, '暂收完成', '', '', '', '', '', localtimestamp, _user_id, _user_no, _user_name, '', localtimestamp, _user_id, _user_no, _user_name, ''
			from me_finish_io_h a
			left join mo b on b.mo_no=a.mo_no
			left join pd_part c on c.part_no=b.part_no
			where me_finish_io_no=_bill_no;
			
			update public.me_finish_io_h set me_finish_io_rmk4='暂收完成',
				upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
			where me_finish_io_no=_bill_no;

			update public.me_finish_io set me_finish_io_rmk4='暂收完成',
				upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
			where me_finish_io_no=_bill_no;

		else
			update public.me_finish_io_h set me_finish_io_rmk4='入库完成',
				upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
			where me_finish_io_no=_bill_no;

			update public.me_finish_io set me_finish_io_rmk4='入库完成',
				upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
			where me_finish_io_no=_bill_no;
		end if;
		*/
		------2025/04/23要求取消分厂回来非华为产品暂收功能---------
			update public.me_finish_io_h set me_finish_io_rmk4='入库完成',
				upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
			where me_finish_io_no=_bill_no;

			update public.me_finish_io set me_finish_io_rmk4='入库完成',
				upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
			where me_finish_io_no=_bill_no;
		--------------------------------------------------------------------------------------------

		res := row('true', '--入库完成--');
		return to_json(res);

	EXCEPTION WHEN OTHERS THEN 
		GET STACKED DIAGNOSTICS 
			_err_msg_text = MESSAGE_TEXT,
			_err_pg_detail = PG_EXCEPTION_DETAIL;
	
		_err_msg := format('错误信息:%s,详情:%s',_err_msg_text,_err_msg_text);
		res := row('false',_err_msg);
		return to_json(res);	
	END;
$function$
;

```
逻辑分析：



          
我需要查看无序列号管控的入库文件内容，然后对比分析两个文件的功能逻辑、业务逻辑和数据逻辑差异。
        
            
toolName: view_files
            
status: success
          
            
filePath: d:\金洋\成品出入库记录\入库\PDA_仓库接受入库（无序列号管控）.md
          
## PDA仓库接受入库（无序列号管控）功能分析

基于 <mcfile name="PDA_仓库接受入库（无序列号管控）.md" path="d:\金洋\成品出入库记录\入库\PDA_仓库接受入库（无序列号管控）.md"></mcfile> 和 <mcfile name="PDA_仓库接收入库.md" path="d:\金洋\成品出入库记录\入库\PDA_仓库接收入库.md"></mcfile> 两个文件的对比分析：

### 无序列号管控入库功能分析

#### 功能概述
函数 `af_pda_wms_prod_inbound_no_sn` 专门处理无序列号管控产品的仓库入库操作。

#### 业务逻辑分析

##### 1. 入库前置条件验证
- **用户验证**：检查PDA用户是否在AX系统中存在
- **入库单验证**：验证入库单是否存在
- **检验结论验证**：必须有合格或特采的检验结论
- **序列号管控验证**：确保产品不是序列号管控产品
- **单据状态验证**：检查入库单状态是否允许入库

##### 2. 组织机构判断
与有序列号管控相同的逻辑：
- `J129` 车间 → `JXML` 组织
- `J171` 车间 → `JXRS` 组织
- 其他车间保持原编号

##### 3. 入库处理逻辑（已简化）
```sql
-- 2025/04/23要求取消分厂回来非华为产品暂收功能
-- 统一更新为"入库完成"状态
update me_finish_io_h set me_finish_io_rmk4='入库完成'
update me_finish_io set me_finish_io_rmk4='入库完成'
```

#### 数据逻辑分析

##### 1. 输入数据结构
```json
{
  "user_no": "用户编号",
  "datas": [{
    "bill_no": "入库单号"
  }]
}
```
**注意**：无需 `sn_no` 字段

##### 2. 核心数据表
- **me_finish_io_h**：生产完工入库单头表
- **me_finish_io**：生产完工入库单明细表
- **qm_si_lot_h**：检验批次头表
- **ss_user**：用户表

##### 3. 状态更新逻辑
直接将 `me_finish_io_rmk4` 字段更新为 "入库完成"

### 两种入库方式的核心区别

| 对比维度 | 有序列号管控入库 | 无序列号管控入库 |
|---------|-----------------|------------------|
| **函数名称** | `af_pda_wms_prod_inbound` | `af_pda_wms_prod_inbound_no_sn` |
| **输入参数** | 需要 `sn_no`（序列号） | 不需要 `sn_no` |
| **验证逻辑** | 基础验证 | 增加序列号管控验证 |
| **处理方式** | 调用子函数分流处理 | 直接更新状态 |
| **业务复杂度** | 复杂（分JX/非JX处理） | 简化（统一处理） |
| **暂收功能** | 支持JX组织暂收 | 已取消暂收功能 |

### 关键业务规则差异

#### 1. 序列号管控验证
无序列号管控入库增加了专门的验证：
```sql
if exists(select distinct 1 from me_finish_io 
          where me_finish_io_no=_bill_no and coalesce(sn_no, '')!='') then
    res := row('false', '此入库单为序列号管控产品，不能使用无序列号管控接收入库.');
end if;
```

#### 2. 检验结论验证
无序列号管控入库增加了检验结论验证：
```sql
if not exists(select 1 from qm_si_lot_h 
              where move_order_no=_bill_no and si_conclusion_name in ('合格','特采')) then
    res := row('false', '此入库单的完工检验单未做检验结论 或者结论不是合格。');
end if;
```

#### 3. 处理流程简化
- **有序列号管控**：根据组织类型调用不同的子函数处理
- **无序列号管控**：直接更新状态为"入库完成"，不再区分JX/非JX组织

#### 4. 暂收功能变更
无序列号管控入库在2025/04/23后取消了分厂产品的暂收功能，统一为直接入库完成。

### 总结
无序列号管控入库是有序列号管控入库的简化版本，主要差异在于：
1. **不需要序列号信息**
2. **增加了更严格的前置验证**
3. **简化了处理流程**
4. **取消了暂收功能**

这种设计体现了对不同产品类型的差异化管理策略，无序列号产品由于管控要求相对简单，因此采用了更直接的入库处理方式。
        