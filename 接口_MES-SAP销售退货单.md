
```sql
 
select 
sa.cr_rtn_h_no,
json_build_object(
'CardCode',sa.client_no,
'DocDate',to_char(sa.upd_time, 'yyyy-mm-dd'),
'OwnerCode','379',
'U_WebNo',sa.cr_rtn_h_no,
'details',
json_agg(json_build_object('ItemCode',sb.part_no,'QuanTity',sb.part_Qty_real,'BatchNo',sb.lot_no,'WhsCode',sb.invp_area,'PriceAfterVAT',sb.price_afvat,'Currency',sb.currency,'EnSetCost',sb.price,'RetCost',sb.stock_price,'U_BaseType','销售交货','U_QtDocNum',sb.so_h_no,'U_QtLine',split_part(sb.so_b_id,'_',2),'U_KHPO',sb.u_khpo)) 
) as datas
from szjy_mes_cr_rtn_h sa
left join szjy_mes_cr_rtn_b sb on sb.cr_rtn_h_id=sa.cr_rtn_h_id
where sa.cr_rtn_rmk06 ='仓库扫描收货完成'
group by sa.cr_rtn_h_no,sa.client_no,sa.upd_time

```


```javascript
function main() {
	var success=[];
	var fail=[];
	
var Prev=Request.Prev;
Log.LogInfo(JSON.stringify(Prev));

for(var i=0;i<Prev.length;i++) {
	var req_params=Prev[i];
	//Log.LogInfo(req_params.me_finish_io_no);
	var cr_rtn_no=req_params.cr_rtn_h_no;

	var params={
		"method": "mes-sap-ORDN",
		"data": JSON.parse(req_params.datas)
	};
	Log.LogInfo(JSON.stringify(params));

	var response = HttpApi.post(
		ss_io_tbl.io_write_auth.io_auth_db_host,
		{body:params}
	);
	var result=JSON.parse(response.Content.ReadAsStringAsync().Result);
	Log.LogInfo(JSON.stringify(result));

	if(result.code!=0){
		fail.push(result.message)
		throw result.message;
	}else{
		success.push(JSON.stringify(result));
		/*ReadContext.ExecuteSql("update cr_rtn_h set sap_bill_no='"+result.message+"',upd_time=localtimestamp where cr_rtn_h_no='"+cr_rtn_no+"'");*/
	}

}

return {"success":success,"fail":fail};

}

```

---

## **MES-SAP销售退货单接口分析报告**

### **1. 接口概述**

这是MES系统与SAP系统之间的销售退货单数据集成接口，负责将MES中已完成收货确认的销售退货数据推送到SAP系统，生成正式的销售退货单(ORDN)。该接口处理客户退货业务，实现退货流程的数字化管控和财务处理。

#### **业务场景**
- **触发时机**: 仓库完成退货商品的扫描收货
- **数据流向**: MES退货系统 → 中间件 → SAP销售退货单
- **业务价值**: 完成退货库存入账、客户账务处理、成本核算
- **适用场景**: 制造企业的客户退货处理、售后服务管理

#### **接口特点**
- **单据类型**: SAP销售退货单(ORDN)
- **业务性质**: 逆向物流处理
- **财务影响**: 库存增加、销售收入冲减
- **质量管控**: 退货商品质量检验和处理

---

## **2. 数据逻辑分析**

### **2.1 数据提取架构图**

```mermaid
graph TD
    A[szjy_mes_cr_rtn_h 退货单头] --> B[数据过滤]
    C[szjy_mes_cr_rtn_b 退货单身] --> B

    B --> D{收货状态检查}
    D -->|仓库扫描收货完成| E[数据聚合处理]
    D -->|其他状态| F[跳过处理]

    E --> G[JSON数据构建]
    G --> H[推送SAP ORDN]

    subgraph "关联数据"
        I[客户信息]
        J[物料信息]
        K[价格信息]
        L[销售订单信息]
    end

    I --> G
    J --> G
    K --> G
    L --> G
```

![1751453534386](D:\金洋\成品出入库记录\入库\assets\1751453534386.png)

### 2.2 核心SQL逻辑分析**

#### **主查询结构**
```sql
select
    sa.cr_rtn_h_no,                    -- 退货单号
    json_build_object(
        'CardCode', sa.client_no,       -- 客户代码
        'DocDate', to_char(sa.upd_time, 'yyyy-mm-dd'),  -- 单据日期
        'OwnerCode', '379',             -- 负责人代码
        'U_WebNo', sa.cr_rtn_h_no,      -- MES单号
        'details', json_agg(明细数据)    -- 退货明细
    ) as datas
from szjy_mes_cr_rtn_h sa              -- 退货单头表
left join szjy_mes_cr_rtn_b sb on sb.cr_rtn_h_id = sa.cr_rtn_h_id  -- 退货单身表
where sa.cr_rtn_rmk06 = '仓库扫描收货完成'  -- 关键过滤条件
group by sa.cr_rtn_h_no, sa.client_no, sa.upd_time
```

#### **明细数据构建逻辑**
```sql
json_agg(json_build_object(
    'ItemCode', sb.part_no,             -- 物料编码
    'QuanTity', sb.part_Qty_real,       -- 实际退货数量
    'BatchNo', sb.lot_no,               -- 批次号
    'WhsCode', sb.invp_area,            -- 仓库代码
    'PriceAfterVAT', sb.price_afvat,    -- 含税价格
    'Currency', sb.currency,            -- 币种
    'EnSetCost', sb.price,              -- 标准成本
    'RetCost', sb.stock_price,          -- 退货成本
    'U_BaseType', '销售交货',           -- 基础单据类型
    'U_QtDocNum', sb.so_h_no,           -- 原销售订单号
    'U_QtLine', split_part(sb.so_b_id,'_',2),  -- 原订单行号
    'U_KHPO', sb.u_khpo                 -- 客户PO号
))
```

### **2.3 数据映射关系表**

| MES字段 | SAP字段 | 数据类型 | 业务含义 | 转换逻辑 |
|---------|---------|----------|----------|----------|
| `client_no` | `CardCode` | varchar | 客户代码 | 直接映射 |
| `upd_time` | `DocDate` | date | 单据日期 | 格式转换 |
| `cr_rtn_h_no` | `U_WebNo` | varchar | MES单号 | 直接映射 |
| `part_no` | `ItemCode` | varchar | 物料编码 | 直接映射 |
| `part_Qty_real` | `QuanTity` | numeric | 退货数量 | 直接映射 |
| `lot_no` | `BatchNo` | varchar | 批次号 | 直接映射 |
| `invp_area` | `WhsCode` | varchar | 仓库代码 | 直接映射 |
| `price_afvat` | `PriceAfterVAT` | numeric | 含税价格 | 直接映射 |
| `currency` | `Currency` | varchar | 币种 | 直接映射 |
| `price` | `EnSetCost` | numeric | 标准成本 | 直接映射 |
| `stock_price` | `RetCost` | numeric | 退货成本 | 直接映射 |
| `so_h_no` | `U_QtDocNum` | varchar | 原销售订单 | 直接映射 |
| `so_b_id` | `U_QtLine` | varchar | 原订单行号 | 字符串分割 |
| `u_khpo` | `U_KHPO` | varchar | 客户PO号 | 直接映射 |

### **2.4 数据转换逻辑深度解析**

#### **订单行号提取逻辑**
```sql
split_part(sb.so_b_id,'_',2)
```
**业务含义**: `so_b_id`格式为"前缀_行号"，需要提取下划线后的行号部分

#### **状态控制逻辑**
```sql
where sa.cr_rtn_rmk06 = '仓库扫描收货完成'
```
**业务含义**: 只有完成仓库收货确认的退货单才能推送到SAP

#### **数据聚合逻辑**
```sql
group by sa.cr_rtn_h_no, sa.client_no, sa.upd_time
```
**业务含义**: 按退货单号分组，将多行明细聚合为一个JSON数组

---

## **3. 业务逻辑分析**

### **3.1 销售退货业务流程图**

```mermaid
sequenceDiagram
    participant Customer as 客户
    participant Sales as 销售部门
    participant Warehouse as 仓库
    participant MES as MES系统
    participant MW as 中间件
    participant SAP as SAP系统

    Customer->>Sales: 申请退货
    Sales->>MES: 创建退货单
    Customer->>Warehouse: 退货商品送达
    Warehouse->>MES: 扫描收货确认
    MES->>MES: 状态更新为"仓库扫描收货完成"

    MES->>MW: 触发退货单接口
    MW->>MES: 查询退货单数据
    MES-->>MW: 返回符合条件的数据

    MW->>MW: 数据转换和格式化
    MW->>SAP: 调用ORDN接口

    alt 成功情况
        SAP-->>MW: 返回退货单号
        MW->>MES: 更新SAP单号(注释状态)
        MW-->>MES: 返回成功结果
        SAP->>SAP: 库存增加、财务处理
    else 失败情况
        SAP-->>MW: 返回错误信息
        MW-->>MES: 抛出异常
    end
```

### **3.2 业务规则分析**

#### **3.2.1 退货业务规则矩阵**
| 业务环节 | 校验规则 | 处理逻辑 | 业务影响 |
|----------|----------|----------|----------|
| **收货确认** | `cr_rtn_rmk06='仓库扫描收货完成'` | 只处理已收货的退货单 | 确保实物到位 |
| **数量处理** | `part_Qty_real` | 使用实际收货数量 | 准确库存核算 |
| **价格处理** | 多种价格字段 | 含税价、成本价分别处理 | 准确财务核算 |
| **原单追溯** | `so_h_no`, `so_b_id` | 关联原销售订单 | 完整业务追溯 |
| **批次管理** | `lot_no` | 批次号完整传递 | 质量追溯管理 |

#### **3.2.2 财务处理逻辑**
- **库存影响**: 退货商品重新入库，增加可用库存
- **成本核算**: 使用`RetCost`字段进行退货成本核算
- **收入处理**: 冲减原销售收入和税金
- **客户账务**: 更新客户应收账款余额

#### **3.2.3 质量管控逻辑**
- **批次追溯**: 通过批次号追溯产品质量问题
- **退货原因**: 可扩展退货原因分类管理
- **质检流程**: 退货商品需要质量检验确认

### **3.3 业务状态流转分析**

```mermaid
stateDiagram-v2
    [*] --> 退货申请
    退货申请 --> 退货审批: 销售确认
    退货审批 --> 客户发货: 审批通过
    客户发货 --> 仓库收货: 商品到达
    仓库收货 --> 扫描确认: 实物验收
    扫描确认 --> SAP推送: 触发本接口
    SAP推送 --> 财务处理: 推送成功
    财务处理 --> [*]: 流程完成

    退货审批 --> 退货申请: 审批拒绝
    SAP推送 --> 扫描确认: 推送失败
```

---

## **4. 代码逻辑分析**

### **4.1 代码架构设计**

#### **处理流程架构**
```
┌─────────────────────────────────────┐
│           数据查询层                 │
│    (SQL + 状态过滤 + 聚合)           │
├─────────────────────────────────────┤
│           数据转换层                 │
│      (JSON构建 + 字段映射)           │
├─────────────────────────────────────┤
│           接口调用层                 │
│       (HTTP + SAP ORDN)             │
├─────────────────────────────────────┤
│           结果处理层                 │
│     (成功记录 + 异常处理)            │
└─────────────────────────────────────┘
```

### **4.2 关键代码逻辑解析**

#### **4.2.1 复杂字段处理**
```sql
-- 订单行号提取
split_part(sb.so_b_id,'_',2)

-- 日期格式转换
to_char(sa.upd_time, 'yyyy-mm-dd')

-- JSON数组聚合
json_agg(json_build_object(...))
```

#### **4.2.2 JavaScript处理逻辑**
```javascript
// 循环处理每个退货单
for(var i=0; i<Prev.length; i++) {
    var req_params = Prev[i];
    var cr_rtn_no = req_params.cr_rtn_h_no;  // 退货单号

    // 构建SAP接口参数
    var params = {
        "method": "mes-sap-ORDN",            // SAP退货单方法
        "data": JSON.parse(req_params.datas) // 解析JSON数据
    };

    // 调用SAP接口
    var response = HttpApi.post(url, {body:params});

    // 处理返回结果
    if(result.code != 0) {
        fail.push(result.message);
        throw result.message;  // 快速失败策略
    } else {
        success.push(JSON.stringify(result));
        // 注意：状态更新代码被注释掉了
        // ReadContext.ExecuteSql("update cr_rtn_h set sap_bill_no=...");
    }
}
```

### **4.3 代码质量分析**

#### **优点**
- ✅ **数据聚合高效**: 使用json_agg避免多次查询
- ✅ **字段映射完整**: 涵盖了价格、成本、追溯等关键信息
- ✅ **错误处理严格**: 失败时立即抛出异常
- ✅ **日志记录详细**: 便于问题排查和调试

#### **潜在问题**
- ⚠️ **状态更新缺失**: 成功后的状态更新代码被注释
- ⚠️ **硬编码值**: OwnerCode固定为'379'
- ⚠️ **异常处理单一**: 缺少细分的错误类型处理
- ⚠️ **事务控制缺失**: 没有回滚机制

#### **关键改进点**
```javascript
// 建议恢复状态更新逻辑
if(result.code == 0) {
    success.push(JSON.stringify(result));
    // 恢复这行代码的执行
    ReadContext.ExecuteSql(
        "update szjy_mes_cr_rtn_h set sap_bill_no='" + result.message +
        "',upd_time=localtimestamp where cr_rtn_h_no='" + cr_rtn_no + "'"
    );
}
```

### **4.4 性能优化分析**

#### **当前优化策略**
- 🚀 **LEFT JOIN优化**: 避免数据丢失
- 🚀 **GROUP BY聚合**: 减少数据传输量
- 🚀 **状态过滤**: 有效减少处理数据量

#### **潜在优化空间**
- 🔧 **批量处理**: 支持多个退货单批量推送
- 🔧 **索引优化**: 在状态字段上创建索引
- 🔧 **分页处理**: 大批量数据分页处理

---

## **5. 表关联逻辑分析**

### **5.1 核心表关联关系图**

```mermaid
erDiagram
    SZJY_MES_CR_RTN_H ||--o{ SZJY_MES_CR_RTN_B : "退货单头身关联"
    SZJY_MES_CR_RTN_B ||--o{ SO_ORDER : "原销售订单关联"
    SZJY_MES_CR_RTN_B ||--o{ PD_PART : "物料信息关联"
    SZJY_MES_CR_RTN_B ||--o{ CLIENT : "客户信息关联"

    SZJY_MES_CR_RTN_H {
        varchar cr_rtn_h_id PK "退货单头ID"
        varchar cr_rtn_h_no "退货单号"
        varchar client_no FK "客户代码"
        timestamp upd_time "更新时间"
        varchar cr_rtn_rmk06 "状态备注"
        varchar sap_bill_no "SAP单号"
    }

    SZJY_MES_CR_RTN_B {
        varchar cr_rtn_b_id PK "退货单身ID"
        varchar cr_rtn_h_id FK "退货单头ID"
        varchar part_no FK "物料编码"
        numeric part_Qty_real "实际退货数量"
        varchar lot_no "批次号"
        varchar invp_area "仓库代码"
        numeric price_afvat "含税价格"
        varchar currency "币种"
        numeric price "标准成本"
        numeric stock_price "退货成本"
        varchar so_h_no FK "原销售订单号"
        varchar so_b_id "原订单行ID"
        varchar u_khpo "客户PO号"
    }

    SO_ORDER {
        varchar so_h_no PK "销售订单号"
        varchar client_no FK "客户代码"
        varchar so_status "订单状态"
    }

    PD_PART {
        varchar part_no PK "物料编码"
        varchar part_name "物料名称"
        varchar part_spec "物料规格"
    }
```

![1751453769587](D:\金洋\成品出入库记录\入库\assets\1751453769587.png)

### **5.2 表关联逻辑详解**

#### **5.2.1 主表关联查询**
```sql
from szjy_mes_cr_rtn_h sa                           -- 主表：退货单头
left join szjy_mes_cr_rtn_b sb on sb.cr_rtn_h_id = sa.cr_rtn_h_id  -- 关联：退货单身
```

#### **5.2.2 关联字段分析**
| 关联类型 | 关联字段 | 获取信息 | 业务用途 |
|----------|----------|----------|----------|
| **头身关联** | `cr_rtn_h_id` | 退货明细信息 | 完整退货数据 |
| **客户关联** | `client_no` | 客户基础信息 | SAP客户代码 |
| **物料关联** | `part_no` | 物料基础信息 | 库存管理 |
| **原单关联** | `so_h_no`, `so_b_id` | 原销售订单信息 | 业务追溯 |

#### **5.2.3 数据完整性保证**
- **LEFT JOIN策略**: 确保主表数据不丢失
- **状态控制**: 通过`cr_rtn_rmk06`字段控制处理范围
- **外键约束**: 通过关联字段保证数据一致性

### **5.3 业务数据流转关系**

```mermaid
graph LR
    subgraph "退货核心数据"
        A[退货单头] --> B[退货单身]
        B --> C[物料信息]
        B --> D[价格信息]
    end

    subgraph "原单追溯数据"
        E[原销售订单] --> F[客户信息]
        E --> G[订单行信息]
    end

    subgraph "SAP集成数据"
        H[退货单号] --> I[财务处理]
        I --> J[库存更新]
    end

    A --> E
    B --> H
    C --> I
    D --> I
```

---

![1751453839076](D:\金洋\成品出入库记录\入库\assets\1751453839076.png)

## **6. 接口集成架构分析**

### **6.1 SAP ORDN接口协议**

#### **请求数据结构**
```json
{
    "method": "mes-sap-ORDN",
    "data": {
        "CardCode": "客户代码",
        "DocDate": "2024-07-01",
        "OwnerCode": "379",
        "U_WebNo": "退货单号",
        "details": [
            {
                "ItemCode": "物料编码",
                "QuanTity": 100,
                "BatchNo": "批次号",
                "WhsCode": "仓库代码",
                "PriceAfterVAT": 1000.00,
                "Currency": "CNY",
                "EnSetCost": 800.00,
                "RetCost": 850.00,
                "U_BaseType": "销售交货",
                "U_QtDocNum": "原销售订单号",
                "U_QtLine": "1",
                "U_KHPO": "客户PO号"
            }
        ]
    }
}
```

#### **响应数据结构**
```json
{
    "code": 0,
    "message": "SAP退货单号",
    "data": {
        "docNum": "退货单号",
        "docEntry": "单据内码"
    }
}
```

### **6.2 与其他接口的关系**

| 接口类型 | 生产入库申请单 | 生产入库单 | 销售退货单 |
|----------|----------------|------------|------------|
| **业务性质** | 生产入库申请 | 生产入库执行 | 销售退货处理 |
| **SAP方法** | mes-sap-Addon | mes-sap-OIGN | mes-sap-ORDN |
| **库存影响** | 无 | 增加库存 | 增加库存 |
| **财务影响** | 无 | 生产成本 | 冲减销售收入 |
| **单据流向** | 正向流程 | 正向流程 | 逆向流程 |

---

## **7. 系统优化建议**

### **7.1 功能完善**
- 🔧 **状态更新恢复**: 恢复成功后的SAP单号更新逻辑
- 🔧 **退货原因管理**: 增加退货原因分类和处理
- 🔧 **质检流程**: 集成退货商品质量检验流程
- 🔧 **批次追溯**: 增强批次号的质量追溯功能

### **7.2 性能优化**
- ⚡ **批量处理**: 支持多个退货单批量推送
- ⚡ **异步处理**: 大批量数据采用异步推送
- ⚡ **索引优化**: 在状态和时间字段上创建索引
- ⚡ **缓存机制**: 缓存客户和物料基础数据

### **7.3 可靠性提升**
- 🛡️ **事务控制**: 增加分布式事务管理
- 🛡️ **重试机制**: 失败自动重试和补偿
- 🛡️ **数据校验**: 推送前完整性校验
- 🛡️ **监控告警**: 异常情况及时告警

### **7.4 业务扩展**
- 📈 **退货分析**: 增加退货数据分析功能
- 📈 **客户管理**: 集成客户退货历史管理
- 📈 **供应商协同**: 支持供应商退货处理
- 📈 **财务集成**: 增强财务处理和报表功能

---

## **总结**

这个MES-SAP销售退货单接口是企业逆向物流管理的重要组件，具有以下特点：

### **技术特色**
- **数据映射完整**: 涵盖价格、成本、追溯等关键业务信息
- **状态控制严格**: 通过收货确认状态确保业务完整性
- **原单追溯清晰**: 完整保留原销售订单的追溯关系
- **财务处理准确**: 支持多种价格和成本的准确核算

### **业务价值**
- **逆向物流管控**: 完整的退货流程数字化管理
- **财务数据准确**: 确保退货对库存和财务的准确影响
- **质量追溯完整**: 通过批次号实现质量问题追溯
- **客户服务提升**: 提高退货处理效率和客户满意度

### **应用场景**
- 制造企业的客户退货管理
- 售后服务流程数字化
- 逆向物流成本核算
- 产品质量问题追溯

### **改进重点**
- 恢复状态更新功能确保数据一致性
- 增加退货原因和质检流程管理
- 提升批量处理和异常处理能力
- 扩展退货数据分析和客户管理功能

该接口为制造企业提供了完整的销售退货数字化解决方案，是客户服务和质量管理体系的重要支撑。

