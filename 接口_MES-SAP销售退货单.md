
	
```javascript
function main() {
	var success=[];
	var fail=[];
	
var Prev=Request.Prev;
Log.LogInfo(JSON.stringify(Prev));

for(var i=0;i<Prev.length;i++) {
	var req_params=Prev[i];
	//Log.LogInfo(req_params.me_finish_io_no);
	var cr_rtn_no=req_params.cr_rtn_h_no;

	var params={
		"method": "mes-sap-ORDN",
		"data": JSON.parse(req_params.datas)
	};
	Log.LogInfo(JSON.stringify(params));

	var response = HttpApi.post(
		ss_io_tbl.io_write_auth.io_auth_db_host,
		{body:params}
	);
	var result=JSON.parse(response.Content.ReadAsStringAsync().Result);
	Log.LogInfo(JSON.stringify(result));

	if(result.code!=0){
		fail.push(result.message)
		throw result.message;
	}else{
		success.push(JSON.stringify(result));
		/*ReadContext.ExecuteSql("update cr_rtn_h set sap_bill_no='"+result.message+"',upd_time=localtimestamp where cr_rtn_h_no='"+cr_rtn_no+"'");*/
	}

}

return {"success":success,"fail":fail};

}

```