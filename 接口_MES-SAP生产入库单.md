
```sql
select ma.me_finish_io_no,
case when strpos(ma.mo_no,'F')=0 then
json_build_object('DocDate',to_char(ma.finish_io_datetime,'yyyy-mm-dd'),'U_MES','Y','U_WebNo',ma.me_finish_io_no,'details',
json_agg(json_build_object('BaseType',202,'BaseRef',ma.mo_no,'WhsCode',mc.invp_area_no,'QuanTity',finish_io_qty_ok,'BatchNo',lot_no,'AcctCode',mb.acct_code,'U_BaseType','FQCTEMP','U_QtDocNum',substring(ma.sap_inbound_apply_no,strpos(ma.sap_inbound_apply_no,'：')+1),'U_QtLine','1')))
else json_build_object('DocDate',to_char(ma.finish_io_datetime,'yyyy-mm-dd'),'U_MES','Y','U_WebNo',ma.me_finish_io_no,'details',
json_agg(json_build_object('ItemCode',ma.part_no,'WhsCode',mc.invp_area_no,'QuanTity',ma.finish_io_qty_ok,'BatchNo',ma.lot_no,'AcctCode',mb.acct_code,'U_BaseType','SPLIT1','U_OPLANNUM',substring(ma.sap_inbound_apply_no,strpos(ma.sap_inbound_apply_no,'：')+1),'U_Reqline','1','U_QtDocNum',replace(ma.mo_no,'F',''),'U_QtLine','1'))) end  as datas
from me_finish_io_h ma
left join mo mb on mb.mo_no=ma.mo_no
left join pd_part mc on mc.part_no=ma.part_no
where ma.me_finish_io_rmk4='入库完成' and coalesce(ma.sap_inbound_apply_no,'')!='' and coalesce(ma.sap_inbound_no,'')='' and ma.me_finish_io_no='SCRK202507010024' 
group by ma.me_finish_io_no

```

```javascript
function main() {
	var success=[];
	var fail=[];
	
	var Prev=Request.Prev;
	Log.LogInfo(JSON.stringify(Prev));

	for(var i=0;i<Prev.length;i++) {
		var req_params=Prev[i];
		Log.LogInfo(req_params.me_finish_io_no);
		var finish_io_no=req_params.me_finish_io_no;

		var params={
			"method": "mes-sap-OIGN",
			"data": JSON.parse(req_params.datas)
		};
		Log.LogInfo(JSON.stringify(params));

		var response = HttpApi.post(
			ss_io_tbl.io_write_auth.io_auth_db_host,
			{body:params}
		);
		var result=JSON.parse(response.Content.ReadAsStringAsync().Result);
		Log.LogInfo(JSON.stringify(result));

		if(result.code!=0){
			fail.push(result.message)
			throw result.message;
		}else{
			success.push(JSON.stringify(result));
			ReadContext.ExecuteSql("update me_finish_io_h set sap_inbound_no='"+result.message+"',upd_time=localtimestamp where me_finish_io_no='"+finish_io_no+"'");
		}

	}

	return {"success":success,"fail":fail};
}



```

---

## **MES-SAP生产入库单接口分析报告**

### **1. 接口概述**

这是MES系统与SAP系统之间的生产入库单数据集成接口，负责将MES中已完成质检的生产完工数据推送到SAP系统，生成正式的生产入库单(OIGN)。该接口是生产入库申请单的后续流程，实现从申请到正式入库的完整闭环。

#### **业务场景**
- **前置条件**: 生产入库申请单已在SAP中创建并审批通过
- **触发时机**: MES完工单状态变更为"入库完成"
- **数据流向**: MES → 中间件 → SAP OIGN(生产入库单)
- **业务价值**: 完成库存的正式入账，更新SAP财务和库存数据

#### **与申请单接口的区别**
| 对比项 | 申请单接口 | 入库单接口 |
|--------|------------|------------|
| **SAP单据类型** | 申请单(Addon) | 入库单(OIGN) |
| **业务阶段** | 申请阶段 | 执行阶段 |
| **前置条件** | 完工送检完成 | 申请单审批+入库完成 |
| **数据状态** | `sap_inbound_apply_no != ''` | `me_finish_io_rmk4='入库完成'` |
| **库存影响** | 无 | 正式入库 |

---

## **2. 数据逻辑分析**

### **2.1 数据提取逻辑架构**

```mermaid
graph TD
    A[me_finish_io_h 完工单头] --> B{工单类型判断}
    B -->|正常工单| C[标准入库逻辑]
    B -->|F开头重工单| D[分切单入库逻辑]

    C --> E[关联mo工单表]
    D --> E
    E --> F[关联pd_part物料表]
    F --> G[构建JSON数据]

    G --> H[推送SAP OIGN]

    subgraph "数据过滤条件"
        I[入库完成状态]
        J[申请单号不为空]
        K[入库单号为空]
    end

    I --> A
    J --> A
    K --> A
```

![1751453030599](D:\金洋\成品出入库记录\入库\assets\1751453030599.png)

### 2.2 核心SQL逻辑分析**

#### **数据提取主逻辑**
```sql
select ma.me_finish_io_no,
case when strpos(ma.mo_no,'F')=0 then
    -- 正常工单处理逻辑
    json_build_object(
        'DocDate', to_char(ma.finish_io_datetime,'yyyy-mm-dd'),
        'U_MES', 'Y',
        'U_WebNo', ma.me_finish_io_no,
        'details', json_agg(正常工单明细)
    )
else
    -- 重工单(F开头)处理逻辑
    json_build_object(
        'DocDate', to_char(ma.finish_io_datetime,'yyyy-mm-dd'),
        'U_MES', 'Y',
        'U_WebNo', ma.me_finish_io_no,
        'details', json_agg(重工单明细)
    )
end as datas
```

#### **关键过滤条件分析**
```sql
where ma.me_finish_io_rmk4='入库完成'           -- 业务状态：已完成入库
  and coalesce(ma.sap_inbound_apply_no,'')!='' -- 申请单号：已有申请单
  and coalesce(ma.sap_inbound_no,'')=''        -- 入库单号：未生成入库单
  and ma.me_finish_io_no='SCRK202507010024'    -- 特定单号(测试用)
```

### **2.3 数据映射关系详解**

#### **2.3.1 正常工单数据映射**
| MES字段 | SAP字段 | 数据类型 | 业务含义 | 转换逻辑 |
|---------|---------|----------|----------|----------|
| `finish_io_datetime` | `DocDate` | date | 入库日期 | 格式转换 |
| `me_finish_io_no` | `U_WebNo` | varchar | MES单号 | 直接映射 |
| `mo_no` | `BaseRef` | varchar | 基础单据号 | 直接映射 |
| `invp_area_no` | `WhsCode` | varchar | 仓库代码 | 关联查询 |
| `finish_io_qty_ok` | `QuanTity` | numeric | 入库数量 | 直接映射 |
| `lot_no` | `BatchNo` | varchar | 批次号 | 直接映射 |
| `acct_code` | `AcctCode` | varchar | 会计科目 | 关联查询 |
| `sap_inbound_apply_no` | `U_QtDocNum` | varchar | 申请单号 | 字符串截取 |

#### **2.3.2 重工单数据映射**
| MES字段 | SAP字段 | 数据类型 | 业务含义 | 转换逻辑 |
|---------|---------|----------|----------|----------|
| `part_no` | `ItemCode` | varchar | 物料编码 | 直接映射 |
| `mo_no` | `U_QtDocNum` | varchar | 原工单号 | 去掉F前缀 |
| `sap_inbound_apply_no` | `U_OPLANNUM` | varchar | 申请单号 | 字符串截取 |

### **2.4 数据转换逻辑深度解析**

#### **申请单号提取逻辑**
```sql
-- 从申请单号中提取SAP单据号
substring(ma.sap_inbound_apply_no, strpos(ma.sap_inbound_apply_no,'：')+1)
```
**业务含义**: 申请单号格式为"前缀：SAP单号"，需要提取冒号后的部分

#### **重工单号处理逻辑**
```sql
-- 重工单去掉F前缀
replace(ma.mo_no,'F','')
```
**业务含义**: 重工单以F开头，SAP中需要使用原始工单号

---

## **3. 业务逻辑分析**

### **3.1 业务流程时序图**

```mermaid
sequenceDiagram
    participant MES as MES系统
    participant MW as 中间件
    participant SAP as SAP系统

    Note over MES,SAP: 前置：申请单已审批通过

    MES->>MES: 质检完成
    MES->>MES: 状态更新为"入库完成"
    MES->>MW: 触发入库单接口

    MW->>MES: 查询完工单数据
    MES-->>MW: 返回符合条件的数据

    MW->>MW: 数据转换和格式化
    MW->>SAP: 调用OIGN接口

    alt 成功情况
        SAP-->>MW: 返回入库单号
        MW->>MES: 更新sap_inbound_no
        MW-->>MES: 返回成功结果
    else 失败情况
        SAP-->>MW: 返回错误信息
        MW-->>MES: 抛出异常
    end
```

### **3.2 业务规则矩阵**

| 业务场景 | 判断条件 | 处理逻辑 | SAP字段差异 | 业务影响 |
|----------|----------|----------|-------------|----------|
| **正常工单** | `strpos(mo_no,'F')=0` | 标准入库流程 | `BaseType=202`, `BaseRef=mo_no` | 标准库存入账 |
| **重工单** | `strpos(mo_no,'F')=1` | 分切单入库流程 | `ItemCode=part_no`, `U_BaseType=SPLIT1` | 分切库存入账 |
| **申请单关联** | 提取申请单号 | 建立申请-入库关联 | `U_QtDocNum`, `U_OPLANNUM` | 单据追溯 |
| **状态控制** | 多重状态检查 | 防止重复入库 | - | 数据一致性 |

### **3.3 业务状态流转分析**

```mermaid
stateDiagram-v2
    [*] --> 完工送检
    完工送检 --> 申请单生成: 送检通过
    申请单生成 --> 申请单审批: SAP审批流程
    申请单审批 --> 入库完成: MES确认入库
    入库完成 --> 入库单生成: 触发本接口
    入库单生成 --> 库存入账: SAP处理
    库存入账 --> [*]: 流程完成

    申请单审批 --> 申请单生成: 审批拒绝
    入库单生成 --> 入库完成: 生成失败
```

![1751453154346](D:\金洋\成品出入库记录\入库\assets\1751453154346.png)

### **3.4 异常处理业务逻辑**

#### **数据完整性校验**
- **申请单号校验**: 必须存在有效的申请单号
- **状态校验**: 必须为"入库完成"状态
- **重复性校验**: 防止同一完工单重复生成入库单

#### **错误处理策略**
```javascript
if(result.code != 0) {
    fail.push(result.message);
    throw result.message;  // 抛出异常，中断处理
}
```
**业务含义**: 采用"快速失败"策略，任何错误都会中断整个批次处理

---

## **4. 代码逻辑分析**

### **4.1 代码架构设计**

#### **分层架构**
```
┌─────────────────────────────────────┐
│           业务控制层                 │
│        (JavaScript Main)           │
├─────────────────────────────────────┤
│           数据转换层                 │
│         (SQL + JSON)               │
├─────────────────────────────────────┤
│           接口调用层                 │
│        (HTTP + SAP API)            │
├─────────────────────────────────────┤
│           数据持久层                 │
│      (Database Update)             │
└─────────────────────────────────────┘
```

### **4.2 关键代码逻辑解析**

#### **4.2.1 复杂SQL构建逻辑**
```sql
-- 使用CASE WHEN实现条件分支
case when strpos(ma.mo_no,'F')=0 then
    -- 正常工单的JSON构建
    json_build_object(...)
else
    -- 重工单的JSON构建
    json_build_object(...)
end
```

#### **4.2.2 字符串处理技巧**
```sql
-- 申请单号提取
substring(ma.sap_inbound_apply_no, strpos(ma.sap_inbound_apply_no,'：')+1)

-- 重工单号处理
replace(ma.mo_no,'F','')
```

#### **4.2.3 JSON聚合处理**
```sql
-- 将多行明细聚合为JSON数组
json_agg(json_build_object(
    'BaseType', 202,
    'BaseRef', ma.mo_no,
    'WhsCode', mc.invp_area_no,
    -- ... 其他字段
))
```

### **4.3 代码质量分析**

#### **优点**
- ✅ **条件分支清晰**: 正常工单和重工单逻辑分离
- ✅ **数据聚合高效**: 使用json_agg避免多次查询
- ✅ **错误处理严格**: 失败时立即抛出异常
- ✅ **日志记录完整**: 关键步骤都有日志输出

#### **潜在改进点**
- ⚠️ **SQL复杂度高**: 单个SQL包含过多业务逻辑
- ⚠️ **硬编码较多**: BaseType、行号等值硬编码
- ⚠️ **异常处理粗糙**: 缺少细分的错误类型处理
- ⚠️ **事务控制缺失**: 没有回滚机制

### **4.4 性能优化分析**

#### **当前优化点**
- 🚀 **LEFT JOIN优化**: 使用左连接避免数据丢失
- 🚀 **GROUP BY聚合**: 减少数据传输量
- 🚀 **条件过滤**: WHERE子句有效过滤数据

#### **潜在优化空间**
- 🔧 **索引优化**: 建议在过滤字段上创建索引
- 🔧 **分页处理**: 大批量数据需要分页处理
- 🔧 **连接池**: HTTP连接复用提高效率

---

## **5. 表关联逻辑分析**

### **5.1 核心表关联关系图**

```mermaid
erDiagram
    ME_FINISH_IO_H ||--o{ MO : "工单关联"
    ME_FINISH_IO_H ||--o{ PD_PART : "物料关联"
    MO ||--o{ PD_PART : "工单物料关联"

    ME_FINISH_IO_H {
        varchar me_finish_io_no PK "完工单号"
        varchar mo_no FK "工单号"
        varchar part_no FK "物料编码"
        timestamp finish_io_datetime "完工时间"
        numeric finish_io_qty_ok "合格数量"
        varchar lot_no "批次号"
        varchar me_finish_io_rmk4 "状态备注"
        varchar sap_inbound_apply_no "申请单号"
        varchar sap_inbound_no "入库单号"
        timestamp upd_time "更新时间"
    }

    MO {
        varchar mo_no PK "工单号"
        varchar part_no FK "物料编码"
        varchar acct_code "会计科目"
        varchar mo_type "工单类型"
    }

    PD_PART {
        varchar part_no PK "物料编码"
        varchar part_name "物料名称"
        varchar invp_area_no "库区代码"
        varchar part_spec "物料规格"
    }
```

### **5.2 表关联逻辑详解**

#### **5.2.1 主表关联查询**
```sql
from me_finish_io_h ma                    -- 主表：完工单头
left join mo mb on mb.mo_no = ma.mo_no    -- 关联：工单信息
left join pd_part mc on mc.part_no = ma.part_no  -- 关联：物料信息
```

#### **5.2.2 关联字段用途分析**
| 关联表 | 关联字段 | 获取信息 | 业务用途 |
|--------|----------|----------|----------|
| `mo` | `mo_no` | `acct_code` | SAP会计科目设置 |
| `pd_part` | `part_no` | `invp_area_no` | 确定入库仓库 |

#### **5.2.3 数据完整性保证**
- **LEFT JOIN策略**: 确保主表数据不丢失
- **关联字段校验**: 通过外键关系保证数据一致性
- **空值处理**: 使用COALESCE处理可能的空值

### **5.3 业务数据流转关系**

```mermaid
graph LR
    subgraph "MES核心表"
        A[me_finish_io_h] --> B[mo]
        A --> C[pd_part]
    end

    subgraph "SAP集成数据"
        D[申请单号] --> E[入库单号]
        E --> F[库存更新]
    end

    subgraph "状态控制"
        G[me_finish_io_rmk4] --> H[sap_inbound_apply_no]
        H --> I[sap_inbound_no]
    end

    A --> D
    A --> G
```

---

## **6. 接口集成架构分析**

### **6.1 SAP OIGN接口协议**

#### **请求数据结构**
```json
{
    "method": "mes-sap-OIGN",
    "data": {
        "DocDate": "2024-07-01",
        "U_MES": "Y",
        "U_WebNo": "SCRK202507010024",
        "details": [
            {
                // 正常工单字段
                "BaseType": 202,
                "BaseRef": "MO202407001",
                "WhsCode": "01",
                "QuanTity": 100,
                "BatchNo": "MES888888",
                "AcctCode": "1405001",
                "U_BaseType": "FQCTEMP",
                "U_QtDocNum": "SAP申请单号",
                "U_QtLine": "1"
            },
            {
                // 重工单字段
                "ItemCode": "PART001",
                "WhsCode": "01",
                "QuanTity": 50,
                "BatchNo": "MES888888",
                "AcctCode": "1405001",
                "U_BaseType": "SPLIT1",
                "U_OPLANNUM": "SAP申请单号",
                "U_Reqline": "1",
                "U_QtDocNum": "原工单号",
                "U_QtLine": "1"
            }
        ]
    }
}
```

#### **响应数据结构**
```json
{
    "code": 0,
    "message": "SAP入库单号",
    "data": {
        "docNum": "入库单号",
        "docEntry": "单据内码"
    }
}
```

### **6.2 与申请单接口的协同关系**

| 接口阶段 | 申请单接口 | 入库单接口 |
|----------|------------|------------|
| **数据来源** | 完工送检数据 | 申请单审批后数据 |
| **SAP方法** | mes-sap-Addon | mes-sap-OIGN |
| **状态字段** | `sap_inbound_apply_no` | `sap_inbound_no` |
| **业务影响** | 申请入库 | 实际入库 |
| **库存变化** | 无 | 增加库存 |

---

## **7. 系统优化建议**

### **7.1 架构层面优化**
- 🏗️ **微服务拆分**: 将申请单和入库单接口独立部署
- 🏗️ **消息队列**: 使用异步消息处理大批量数据
- 🏗️ **配置中心**: 统一管理接口配置和映射关系
- 🏗️ **服务网关**: 统一接口访问入口和安全控制

### **7.2 性能优化**
- ⚡ **SQL优化**: 拆分复杂SQL，提高可读性和性能
- ⚡ **批量处理**: 支持多个完工单批量推送
- ⚡ **缓存机制**: 缓存物料和工单基础数据
- ⚡ **连接池**: 优化HTTP连接管理

### **7.3 可靠性提升**
- 🛡️ **事务管理**: 增加分布式事务控制
- 🛡️ **重试机制**: 失败自动重试和补偿机制
- 🛡️ **监控告警**: 接口调用监控和异常告警
- 🛡️ **数据校验**: 推送前数据完整性校验

### **7.4 维护性改进**
- 🔧 **代码重构**: 提取公共逻辑，减少重复代码
- 🔧 **配置化**: 硬编码值移到配置表管理
- 🔧 **日志增强**: 结构化日志和链路追踪
- 🔧 **文档完善**: API文档和业务流程文档

---

## **总结**

这个MES-SAP生产入库单接口是企业ERP集成的核心组件，具有以下特点：

### **技术特色**
- **复杂业务逻辑**: 支持正常工单和重工单的差异化处理
- **数据转换能力**: 灵活的JSON构建和字符串处理
- **状态管控严格**: 多重条件确保数据一致性
- **集成架构清晰**: SQL数据层 + JavaScript业务层

### **业务价值**
- **流程闭环**: 完成从申请到入库的完整业务闭环
- **库存准确**: 确保SAP库存数据的实时性和准确性
- **财务一致**: 保证生产成本和库存价值的一致性
- **追溯完整**: 建立MES和SAP之间的完整数据追溯链

### **应用场景**
- 制造企业的生产入库管理
- ERP系统间的数据同步
- 库存和财务数据的实时更新
- 生产流程的数字化管控

该接口为制造企业提供了可靠的生产入库数据同步能力，是数字化工厂建设的重要基础设施。
